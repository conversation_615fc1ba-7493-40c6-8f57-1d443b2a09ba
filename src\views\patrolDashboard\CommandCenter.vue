<template>
  <div class="command-center" :class="{ 'fullscreen-mode': isFullscreen }">
   

    <!-- 主要内容 - 只有在有权限时才显示 -->
    <div  class="command-content">
      <!-- 监区名称标题 -->
      <div class="command-header">
        <div class="header-title">
          <div class="title-glow">{{ prisonName }}</div>
          <!-- <div class="subtitle">实时监控指挥中心</div> -->
        </div>
        <div class="header-actions">
          <div class="header-time">{{ currentTime }}</div>
          <a-button
            v-if="!isFullscreen"
            type="primary"
            class="fullscreen-btn"
            @click="toggleFullscreen"
            :icon="h(FullscreenOutlined)"
          >
            全屏显示
          </a-button>
          <a-button
            v-else
            type="default"
            class="exit-fullscreen-btn"
            @click="exitFullscreen"
            :icon="h(FullscreenExitOutlined)"
          >
            退出全屏
          </a-button>
        </div>
      </div>

    <!-- 巡更人员信息 -->
    <div class="staff-section">
      <div class="section-header">
        <div class="carousel-controls">
          <a-button
            type="text"
            size="small"
            @click="toggleCarouselMode"
            :class="{ 'active': carouselMode === 'section' }"
          >
            按监区
          </a-button>
          <a-button
            type="text"
            size="small"
            @click="toggleCarouselMode"
            :class="{ 'active': carouselMode === 'subsection' }"
          >
            按分监区
          </a-button>
        </div>
      </div>

      <!-- 轮播内容 -->
      <div class="carousel-container">
        <transition name="carousel-slide" mode="out-in">
          <div :key="currentCarouselIndex" class="carousel-content">
          
            <!-- 按监区轮播 -->
            <div v-if="carouselMode === 'section'" class="section-carousel">
              <div class="section-group">
                <div class="subsections-container">
                  <div
                    v-for="subSection in currentSection.subSections"
                    :key="subSection.id"
                    class="subsection-group"
                  >
                    <div class="subsection-name">{{ subSection.lineName }}</div>
                    <div class="staff-cards">
                      <div
                        v-for="staff in subSection.cards"
                        :key="staff.id"
                        class="staff-card"
                        :class="{ 'active': staff.online }"
                      >
                        <div class="staff-avatar">
                          <img :src="getFileAccessHttpUrl(staff.patrolUserImage) || getStaffAvatar(staff.patrolUserName)" :alt="staff.patrolUserName" />
                          <!-- 状态指示器：online=true显示巡更中(绿色+动画)，false显示离线(灰色) -->
                          <div class="status-indicator" :class="{ 'online': staff.online, 'offline': !staff.online }"></div>
                        </div>
                        <div class="staff-info">
                          <div class="staff-name">{{ staff.patrolUserName }}</div>
                          <div class="staff-card-id">{{ staff.cardCode }}</div>
                          <!-- 状态文本：online=true显示"巡更中"，false显示"离线" -->
                          <div class="staff-status">{{ staff.online ? '巡更中' : '离线' }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 按分监区轮播 -->
            <div v-else class="subsection-carousel">
              <div class="subsection-group-large">
                <div class="subsection-header">
                  <div class="parent-section">{{ currentSubSection.parentSection }}</div>
                  <div class="subsection-name-large">{{ currentSubSection.name }}</div>
                </div>
                <div class="staff-cards-large">
                  <div
                    v-for="staff in currentSubSection.cards"
                    :key="staff.id"
                    class="staff-card-large"
                    :class="{ 'active': staff.online }"
                  >
                    <div class="staff-avatar-large">
                      <img :src="getFileAccessHttpUrl(staff.patrolUserImage) || getStaffAvatar(staff.patrolUserName)" :alt="staff.patrolUserName" />
                      <!-- 状态指示器：online=true显示巡更中(绿色+动画)，false显示离线(灰色) -->
                      <div class="status-indicator" :class="{ 'online': staff.online, 'offline': !staff.online }"></div>
                    </div>
                    <div class="staff-info-large">
                      <div class="staff-name">{{ staff.patrolUserName }}</div>
                      <div class="staff-card-id">{{ staff.cardCode }}</div>
                      <!-- 状态文本：online=true显示"巡更中"，false显示"离线" -->
                      <div class="staff-status">{{ staff.online ? '巡更中' : '离线' }}</div>
                      <div class="staff-department">{{ staff.lineName }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>

      <!-- 轮播指示器移动到下方 -->
      <div class="carousel-indicators">
        <div class="carousel-info">
          <span v-if="carouselMode === 'section'">
            {{ currentCarouselIndex + 1 }} / {{ commandData.sections.length }} 监区
          </span>
          <span v-else>
            {{ currentCarouselIndex + 1 }} / {{ totalSubSections }} 分监区
          </span>
        </div>
        <div class="carousel-dots">
          <div
            v-for="(item, index) in carouselItems"
            :key="index"
            class="dot"
            :class="{ 'active': index === currentCarouselIndex }"
            @click="setCarouselIndex(index)"
          ></div>
        </div>
        <div class="carousel-nav">
          <a-button type="text" size="small" @click="prevCarousel" :icon="h(LeftOutlined)"></a-button>
          <a-button type="text" size="small" @click="nextCarousel" :icon="h(RightOutlined)"></a-button>
        </div>
      </div>
    </div>

    <!-- 实时巡更计划状态 -->
    <div class="patrol-plans-section">
      <div class="section-title">
        <div class="title-line"></div>
        <span>实时巡更记录</span>
        <div class="title-line"></div>
      </div>

      <div class="plans-container">
        <div class="plans-scroll" ref="plansScrollRef">
          <div
            v-for="plan in commandData.plans"
            :key="plan.id"
            class="plan-item"
            :class="getPlanStatusClass(plan.status)"
          >
            <!-- 计划基本信息 - 一行显示 -->
            <div class="plan-summary">
              <div class="summary-item">
                <span class="label">计划:</span>
                <span class="value">{{ plan.name || '巡更计划' }}</span>
              </div>
              <div class="summary-item">
                <span class="label">监区:</span>
                <span class="value">{{ plan.sectionName }}</span>
              </div>
              <div class="summary-item">
                <span class="label">巡更者:</span>
                <div class="staff-info">
                  <img :src="getFileAccessHttpUrl(plan.patrolUserImage) || defaultAvatar" :alt="plan.patrolUserName" class="staff-avatar-tiny" />
                  <span class="value">{{ plan.patrolUserName }}</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="label">开始:</span>
                <span class="value">{{ plan.startTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.endTime">
                <span class="label">结束:</span>
                <span class="value">{{ plan.endTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.status === '2' && plan.patrolDuration">
                <span class="label">用时:</span>
                <span class="value">{{ plan.patrolDuration }}</span>
              </div>
              <div class="plan-status-badge" :class="getPlanStatusClass(plan.status)">
                {{ getStatusText(plan.status) }}
              </div>
            </div>

            <!-- 巡更点状态 - 铺满显示 -->
            <div class="patrol-points">
              <div
                v-for="(point, index) in plan.planCardList"
                :key="point.id"
                class="patrol-point"
                :class="{
                  'is-current': String(plan.status) === '1' && Number(point.status) === 1,
                  'is-next': String(plan.status) === '1' && Number(point.status) === 0 && index === getCurrentPointIndex(plan) + 1
                }"
              >
                <div class="point-icon" :class="`status-${getStatusClass(point.status)}`">
                  <Icon :icon="getPointIcon(point.status)" />
                </div>
                <div class="point-info">
                  <div class="point-name">{{ point.cardName }}</div>
                  <div class="point-time" v-if="point.time">
                    {{ point.time }}
                  </div>
                </div>
                <!-- 连接线 -->
                <div v-if="index < plan.planCardList.length - 1" class="point-connector">
                  <div class="connector-line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, h } from 'vue';
import { Empty } from 'ant-design-vue';
import { getCommandCenterData, generateCommandCenterMockData, type CommandCenterData, type PatrolPlan, type SubSection } from './CommandCenter.api';
import { UserManagedLines } from './PatrolDashboard.api';
import { FullscreenOutlined, FullscreenExitOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import { Icon } from '/@/components/Icon';
import dayjs from 'dayjs';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
// import { getFileAccessHttpUrl } from '/@/utils/common/compUtils'; // 暂时不使用



// 响应式数据
const prisonName = ref('指挥中心可视化大屏');
const currentTime = ref('');
const commandData = ref<CommandCenterData>({
  prisonName: '',
  sections: [],
  totalStatistics: {
    totalStaff: 0,
    onDutyStaff: 0,
    totalPlans: 0,
    inProgressPlans: 0,
    completedPlans: 0,
    missedPlans: 0
  }
});
const plansScrollRef = ref();
const isFullscreen = ref(false);

// 权限相关状态
const hasPermission = ref(false);
const isLoading = ref(true);

// 轮播相关状态
const carouselMode = ref<'section' | 'subsection'>('section'); // 轮播模式：按监区或按分监区
const currentCarouselIndex = ref(0); // 当前轮播索引
const isAutoCarousel = ref(true); // 是否自动轮播

// 默认头像
const defaultAvatar = '/src/assets/images/ai/avatar.jpg';

// 定时器
let timeTimer: NodeJS.Timeout;
let dataTimer: NodeJS.Timeout;
let carouselTimer: NodeJS.Timeout;

// 计算属性 - 排序后的巡更计划
const sortedPatrolPlans = computed(() => {
  const allPlans: PatrolPlan[] = [];
  commandData.value.sections.forEach(section => {
    section.subSections.forEach(subSection => {
      allPlans.push(...subSection.patrolPlans);
    });
  });

  // 按状态排序：进行中 > 待开始 > 已完成 > 已漏巡
  const statusOrder = { 'in-progress': 1, 'pending': 2, 'completed': 3, 'missed': 4 };
  return allPlans.sort((a, b) => statusOrder[a.status] - statusOrder[b.status]);
});

// 计算属性 - 轮播相关
const totalSubSections = computed(() => {
  return commandData.value.sections.reduce((total, section) => total + section.subSections.length, 0);
});

const carouselItems = computed(() => {
  if (carouselMode.value === 'section') {
    return commandData.value.sections;
  } else {
    const allSubSections: (SubSection & { parentSection: string })[] = [];
    commandData.value.sections.forEach(section => {
      section.subSections.forEach(subSection => {
        allSubSections.push({
          ...subSection,
          parentSection: section.name
        });
      });
    });
    return allSubSections;
  }
});

const currentSection = computed(() => {
  if (carouselMode.value === 'section' && commandData.value.sections.length > 0) {
    return commandData.value.sections[currentCarouselIndex.value] || commandData.value.sections[0];
  }
  return commandData.value.sections[0] || { name: '', subSections: [] };
});

const currentSubSection = computed(() => {
  if (carouselMode.value === 'subsection') {
    const allSubSections: (SubSection & { parentSection: string })[] = [];
    commandData.value.sections.forEach(section => {
      section.subSections.forEach(subSection => {
        allSubSections.push({
          ...subSection,
          parentSection: section.name
        });
      });
    });
    return allSubSections[currentCarouselIndex.value] || allSubSections[0] || { name: '', staff: [], patrolPlans: [], parentSection: '' };
  }
  return { name: '', staff: [], patrolPlans: [], parentSection: '' };
});

// 获取状态文本
const getStatusText = (status: string | number) => {
  const statusMap = {
    'on-duty': '值班中',
    'online': '在线',
    'offline': '离线',
    'in-progress': '进行中',
    'pending': '待巡',
    'completed': '已完成',
    'missed': '漏巡',
    'checked': '已巡更',
    'current': '当前位置'
  };

  // 对于数字状态，根据上下文判断
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return '待巡';
      case 1: return '进行中';  // 对于计划状态，1表示进行中
      case 2: return '已完成'; // 对于计划状态，2表示已完成
    }
  }

  return statusMap[String(status)] || String(status);
};

// 获取巡更点图标
const getPointIcon = (status: number | string) => {
  const iconMap = {
    0: 'ant-design:clock-circle-outlined',      // 待巡
    1: 'ant-design:check-circle-filled',        // 已巡更
    2: 'ant-design:close-circle-filled',        // 漏巡
    'pending': 'ant-design:clock-circle-outlined',   // 兼容旧状态
    'checked': 'ant-design:check-circle-filled',     // 兼容旧状态
    'current': 'ant-design:environment-filled',      // 兼容旧状态
    'missed': 'ant-design:close-circle-filled'       // 兼容旧状态
  };
  return iconMap[status] || 'ant-design:clock-circle-outlined';
};

// 获取状态对应的CSS类名（用于巡更点）
const getStatusClass = (status: string | number) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return 'pending';     // 待巡
      case 1: return 'checked';     // 已巡更
      case 2: return 'missed';      // 漏巡
    }
  }
  // 兼容旧状态
  return String(status);
};

// 获取计划状态对应的CSS类名
const getPlanStatusClass = (status: string | number) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return 'pending';       // 待巡
      case 1: return 'in-progress';   // 进行中
      case 2: return 'completed';     // 已完成
    }
  }

  // 兼容旧状态
  const statusStr = String(status);
  switch (statusStr) {
    case 'pending': return 'pending';
    case 'in-progress': return 'in-progress';
    case 'completed': return 'completed';
    case 'missed': return 'missed';
    default: return 'pending';
  }
};

// 获取当前巡更点索引
const getCurrentPointIndex = (plan: any) => {
  // 查找数字状态1（已巡更）的最后一个点
  const points = plan.points || [];
  let lastCheckedIndex = -1;

  for (let i = 0; i < points.length; i++) {
    const point = points[i];
    if (Number(point.status) === 1) {
      lastCheckedIndex = i;
    }
  }

  return lastCheckedIndex;
};

// 获取人员头像
const getStaffAvatar = (staff: any) => {
  // 生成基于姓名的SVG头像
  const colors = [
    '#3373dc', '#52c41a', '#ff4d4f', '#fa8c16', '#722ed1',
    '#13c2c2', '#eb2f96', '#faad14', '#1890ff', '#f5222d'
  ];

  // 根据姓名生成颜色索引
  const nameHash = staff.name.split('').reduce((hash: number, char: string) => {
    return hash + char.charCodeAt(0);
  }, 0);
  const colorIndex = nameHash % colors.length;
  const bgColor = colors[colorIndex];

  // 获取姓名的最后一个字符作为显示文字
  const displayText = staff.name.slice(-1);

  // 生成SVG头像 - 修复编码问题
  const svgContent = `<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
<defs>
<linearGradient id="grad${staff.id}" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:${bgColor};stop-opacity:1" />
<stop offset="100%" style="stop-color:${adjustBrightness(bgColor, -20)};stop-opacity:1" />
</linearGradient>
</defs>
<rect width="100" height="100" rx="12" fill="url(#grad${staff.id})"/>
<text x="50" y="65" font-family="Microsoft YaHei, Arial, sans-serif" font-size="32" font-weight="bold" fill="white" text-anchor="middle">${displayText}</text>
</svg>`;

  // 使用更简单的编码方式
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
};

// 调整颜色亮度
const adjustBrightness = (color: string, amount: number) => {
  const usePound = color[0] === '#';
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
};

// 轮播控制方法
const toggleCarouselMode = () => {
  carouselMode.value = carouselMode.value === 'section' ? 'subsection' : 'section';
  currentCarouselIndex.value = 0; // 重置索引
  resetCarouselTimer();
};



const setCarouselIndex = (index: number) => {
  currentCarouselIndex.value = index;
  resetCarouselTimer();
};

const nextCarousel = () => {
  const maxIndex = carouselItems.value.length - 1;
  currentCarouselIndex.value = currentCarouselIndex.value >= maxIndex ? 0 : currentCarouselIndex.value + 1;
  resetCarouselTimer();
};

const prevCarousel = () => {
  const maxIndex = carouselItems.value.length - 1;
  currentCarouselIndex.value = currentCarouselIndex.value <= 0 ? maxIndex : currentCarouselIndex.value - 1;
  resetCarouselTimer();
};

const startCarouselTimer = () => {
  if (isAutoCarousel.value && carouselItems.value.length > 1) {
    carouselTimer = setInterval(() => {
      nextCarousel();
    }, 5000); // 5秒切换一次
  }
};

const stopCarouselTimer = () => {
  if (carouselTimer) {
    clearInterval(carouselTimer);
  }
};

const resetCarouselTimer = () => {
  stopCarouselTimer();
  if (isAutoCarousel.value) {
    startCarouselTimer();
  }
};

// 更新时间
const updateTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
};

// 检查用户权限
const checkUserPermission = async () => {
  try {
    isLoading.value = true;
    const response = await UserManagedLines({});

    // 根据后端返回结果判断权限
    if (response && response.success !== false) {
      hasPermission.value = true;
      // 如果有权限，加载数据
      await loadData();
    } else {
      hasPermission.value = false;
      console.log('用户没有管理路线权限');
    }
  } catch (error) {
    console.error('检查权限失败:', error);
    hasPermission.value = false;
  } finally {
    isLoading.value = false;
  }
};

// 加载数据
const loadData = async () => {
 
  try {
    const response = await getCommandCenterData();
    
    commandData.value = response;
  
  } catch (error) {
    console.warn('API调用失败，使用模拟数据:', error);
    commandData.value = generateCommandCenterMockData();
  }
};

// 全屏相关方法
const toggleFullscreen = () => {
  if (document.documentElement.requestFullscreen) {
    document.documentElement.requestFullscreen();
  }
};

const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  }
};

const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 生命周期
onMounted(() => {
  updateTime();
  loadData();

  // 启动定时器
  timeTimer = setInterval(updateTime, 1000);
  dataTimer = setInterval(loadData, 30000); // 30秒刷新一次数据

  // 启动轮播定时器
  setTimeout(() => {
    startCarouselTimer();
  }, 2000); // 延迟2秒开始轮播，等待数据加载

  // 监听全屏变化
  document.addEventListener('fullscreenchange', handleFullscreenChange);
});

onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer);
  if (dataTimer) clearInterval(dataTimer);
  if (carouselTimer) clearInterval(carouselTimer);
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});
</script>

<style lang="less" scoped>
.command-center {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #2d3561 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow-x: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(255, 0, 150, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  &.fullscreen-mode {
    width: 1920px;
    height: 1080px;
    min-height: 1080px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    transform-origin: top left;

    // 自动缩放适应不同分辨率
    @media (max-width: 1920px) {
      transform: scale(calc(100vw / 1920));
    }

    @media (max-height: 1080px) {
      transform: scale(calc(100vh / 1080));
    }

    @media (max-width: 1920px) and (max-height: 1080px) {
      transform: scale(min(calc(100vw / 1920), calc(100vh / 1080)));
    }
  }
}

/* 头部样式 */
.command-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 150, 255, 0.1) 100%);
  border-bottom: 2px solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);

  .header-title {
    .title-glow {
      font-size: 36px;
      font-weight: bold;
      background: linear-gradient(45deg, #00ffff, #0096ff, #00ffff);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      margin-bottom: 5px;
    }

    .subtitle {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.7);
      text-align: center;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 20px;

    .header-time {
      font-size: 18px;
      color: #00ffff;
      font-weight: 500;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    }

    .fullscreen-btn, .exit-fullscreen-btn {
      background: linear-gradient(45deg, #0096ff, #00ffff);
      border: none;
      color: #ffffff;
      font-weight: 500;
      box-shadow: 0 4px 15px rgba(0, 150, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 150, 255, 0.4);
      }
    }
  }
}

/* 人员信息区域样式 */
.staff-section {
  padding: 20px 40px;

  .section-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;

    .carousel-controls {
      display: flex;
      gap: 10px;

      .ant-btn {
        color: rgba(255, 255, 255, 0.7);
        border-color: rgba(0, 255, 255, 0.3);
        background: rgba(0, 255, 255, 0.05);
        transition: all 0.3s ease;

        &:hover {
          color: #00ffff;
          border-color: #00ffff;
          background: rgba(0, 255, 255, 0.1);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);
        }

        &.active {
          color: #00ffff;
          border-color: #00ffff;
          background: rgba(0, 255, 255, 0.15);
          box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }
      }
    }
  }

  .carousel-indicators {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    margin-bottom: 10px;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(0, 255, 255, 0.1);
    border-radius: 6px;
    backdrop-filter: blur(5px);

    .carousel-info {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;

      span {
        background: linear-gradient(45deg, #00ffff, #0096ff);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .carousel-dots {
      display: flex;
      gap: 8px;
      padding: 6px 10px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 15px;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          border-radius: 50%;
          background: transparent;
          transition: all 0.3s ease;
        }

        &:hover {
          background: rgba(0, 255, 255, 0.6);
          transform: scale(1.3);

          &::before {
            background: rgba(0, 255, 255, 0.2);
          }
        }

        &.active {
          background: #00ffff;
          box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
          transform: scale(1.2);

          &::before {
            background: rgba(0, 255, 255, 0.3);
          }
        }
      }
    }

    .carousel-nav {
      display: flex;
      gap: 6px;

      .ant-btn {
        color: rgba(255, 255, 255, 0.7);
        border-color: rgba(0, 255, 255, 0.3);
        background: rgba(0, 255, 255, 0.05);
        border-radius: 4px;
        transition: all 0.3s ease;
        padding: 4px 8px;
        min-width: 32px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: #00ffff;
          border-color: #00ffff;
          background: rgba(0, 255, 255, 0.1);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);
        }

        &:active {
          transform: translateY(0);
        }

        .anticon {
          font-size: 14px;
        }
      }
    }
  }

  .carousel-container {
    position: relative;
    min-height: 300px;
  }

  .carousel-content {
    width: 100%;
  }

  /* 轮播模式下的监区样式 */
  .section-carousel {
    .section-group {

      .subsections-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;

        .subsection-group {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(0, 255, 255, 0.2);
          border-radius: 12px;
          padding: 20px;
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.1);
          }

          .subsection-name {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            text-align: center;
            margin-bottom: 18px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 255, 255, 0.2);
          }

          .staff-cards {
            display: flex;
            gap: 12px;
            justify-content: center;

            .staff-card {
              background: rgba(255, 255, 255, 0.08);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 10px;
              padding: 15px;
              text-align: center;
              transition: all 0.3s ease;
              position: relative;
              overflow: hidden;
              flex: 1;
              max-width: 120px;

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
                transition: left 0.5s ease;
              }

              &:hover::before {
                left: 100%;
              }

              &.active {
                border-color: #00ffff;
                box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);

                .staff-avatar {
                  animation: pulse 2s infinite;
                }
              }

              .staff-avatar {
                position: relative;
                margin: 0 auto 10px;
                width: 70px;
                height: 70px;

                img {
                  width: 100%;
                  height: 100%;
                  border-radius: 10px;
                  object-fit: cover;
                  border: 2px solid rgba(255, 255, 255, 0.2);
                  background: rgba(255, 255, 255, 0.05);
                  transition: all 0.3s ease;
                }

                &:hover img {
                  border-color: rgba(0, 255, 255, 0.4);
                  box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
                }

                .status-indicator {
                  position: absolute;
                  bottom: -2px;
                  right: -2px;
                  width: 18px;
                  height: 18px;
                  border-radius: 50%;
                  border: 2px solid #ffffff;
                  z-index: 2;

                  &.on-duty {
                    background: #00ff00;
                    box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
                    animation: pulse 2s infinite;
                  }

                  &.online {
                    background: #00ff00; // 巡更中 - 绿色
                    box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
                    animation: pulse 2s infinite; // 脉冲动画
                  }

                  &.offline {
                    background: #666666; // 离线 - 灰色
                    box-shadow: none;
                  }
                }
              }

              .staff-info {
                .staff-name {
                  font-size: 15px;
                  font-weight: 600;
                  color: #ffffff;
                  margin-bottom: 5px;
                }

                .staff-card-id {
                  font-size: 13px;
                  color: rgba(255, 255, 255, 0.7);
                  margin-bottom: 5px;
                }

                .staff-status {
                  font-size: 12px;
                  color: #00ffff;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 255, 255, 0);
  }
}

/* 轮播动画 */
.carousel-slide-enter-active,
.carousel-slide-leave-active {
  transition: all 0.5s ease;
}

.carousel-slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.carousel-slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 分监区轮播样式 */
.subsection-carousel {
  .subsection-group-large {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 12px;
    padding: 30px;
    backdrop-filter: blur(10px);

    .subsection-header {
      text-align: center;
      margin-bottom: 30px;

      .parent-section {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 10px;
      }

      .subsection-name-large {
        font-size: 28px;
        font-weight: bold;
        color: #00ffff;
        text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
      }
    }

    .staff-cards-large {
      display: flex;
      gap: 30px;
      justify-content: center;

      .staff-card-large {
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 25px;
        text-align: center;
        transition: all 0.3s ease;
        flex: 1;
        max-width: 200px;

        &.active {
          border-color: #00ffff;
          box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);

          .staff-avatar-large {
            animation: pulse 2s infinite;
          }
        }

        .staff-avatar-large {
          position: relative;
          margin: 0 auto 15px;
          width: 100px;
          height: 100px;

          img {
            width: 100%;
            height: 100%;
            border-radius: 12px;
            object-fit: cover;
            border: 3px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
          }

          &:hover img {
            border-color: rgba(0, 255, 255, 0.5);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            transform: scale(1.05);
          }

          .status-indicator {
            position: absolute;
            bottom: -3px;
            right: -3px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid #ffffff;
            z-index: 2;

            &.on-duty {
              background: #00ff00;
              box-shadow: 0 0 12px rgba(0, 255, 0, 0.6);
              animation: pulse 2s infinite;
            }

            &.online {
              background: #00ff00; // 巡更中 - 绿色
              box-shadow: 0 0 12px rgba(0, 255, 0, 0.6);
              animation: pulse 2s infinite; // 脉冲动画
            }

            &.offline {
              background: #666666; // 离线 - 灰色
              box-shadow: none;
            }
          }
        }

        .staff-info-large {
          .staff-name {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
          }

          .staff-card-id {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 6px;
          }

          .staff-status {
            font-size: 12px;
            color: #00ffff;
            margin-bottom: 6px;
          }

          .staff-department {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }
  }
}

/* 巡更计划区域样式 */
.patrol-plans-section {
  padding: 10px 40px 20px 40px;
  flex: 1;
  display: flex;
  flex-direction: column;

  .section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;

    .title-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    span {
      margin: 0 20px;
      font-size: 24px;
      font-weight: bold;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    }
  }

  .plans-container {
    flex: 1;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);

    .plans-scroll {
      height: 100%;
      max-height: 500px;
      overflow-y: auto;
      padding: 20px;

      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, #00ffff, #0096ff);
        border-radius: 4px;

        &:hover {
          background: linear-gradient(45deg, #0096ff, #00ffff);
        }
      }

      .plan-item {
        padding: clamp(12px, 1.5vw, 18px);
        background: rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        flex-shrink: 0;
        margin-bottom: clamp(12px, 1.5vh, 20px);

        @media (max-width: 767px) {
          border-radius: 8px;
          margin-bottom: 15px;
          padding: 15px;
        }

        &.in-progress {
          border-color: #1890ff;
          box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);

          .plan-status-badge {
            background: linear-gradient(45deg, #1890ff, #40a9ff);
          }
        }

        &.pending {
          border-color: #faad14;

          .plan-status-badge {
            background: linear-gradient(45deg, #faad14, #ffc53d);
          }
        }

        &.completed {
          border-color: #52c41a;

          .plan-status-badge {
            background: linear-gradient(45deg, #52c41a, #73d13d);
          }
        }

        &.missed {
          border-color: #ff4d4f;

          .plan-status-badge {
            background: linear-gradient(45deg, #ff4d4f, #ff7875);
          }
        }

        &:hover {
          transform: translateX(5px);
          box-shadow: 0 3px 15px rgba(0, 255, 255, 0.2);
        }

        .plan-summary {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: clamp(10px, 1.2vh, 15px);
          padding: clamp(10px, 1.2vh, 15px);
          background: rgba(0, 0, 0, 0.2);
          border-radius: 8px;
          border: 1px solid rgba(0, 255, 255, 0.2);
          position: relative;
          overflow: hidden;

          @media (max-width: 767px) {
            flex-wrap: wrap;
            gap: 8px;
          }

          /* 添加实时进度条 */
          &::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            animation: progressBar 10s linear infinite;
            box-shadow: 0 0 6px rgba(24, 144, 255, 0.6);
          }

          .summary-item {
            display: flex;
            align-items: center;
            gap: 6px;

            @media (max-width: 767px) {
              gap: 4px;
            }

            .label {
              font-size: clamp(12px, 1.2vw, 14px);
              color: rgba(0, 255, 255, 0.8);
              font-weight: bold;
              white-space: nowrap;

              @media (max-width: 767px) {
                font-size: 11px;
              }
            }

            .value {
              font-size: clamp(12px, 1.2vw, 14px);
              color: #ffffff;
              font-weight: normal;
              white-space: nowrap;

              @media (max-width: 767px) {
                font-size: 11px;
              }
            }

            .staff-info {
              display: flex;
              align-items: center;
              gap: 6px;

              .staff-avatar-tiny {
                width: clamp(18px, 1.8vw, 24px);
                height: clamp(18px, 1.8vw, 24px);
                border-radius: 4px;
                border: 1px solid rgba(0, 255, 255, 0.5);
                object-fit: cover;
                flex-shrink: 0;

                @media (max-width: 767px) {
                  width: 16px;
                  height: 16px;
                }
              }
            }
          }

          .plan-status-badge {
            padding: clamp(5px, 0.8vw, 8px) clamp(12px, 1.5vw, 16px);
            border-radius: 12px;
            font-size: clamp(11px, 1.1vw, 13px);
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
            white-space: nowrap;
            flex-shrink: 0;

            @media (max-width: 767px) {
              padding: 4px 8px;
              font-size: 10px;
            }
          }
        }

        .patrol-points {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: clamp(8px, 1vh, 12px);
          padding: clamp(8px, 1vh, 12px) 0;
          position: relative;

          .patrol-point {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            flex: 1;
            min-width: 0;
            min-height: 60px;

            /* 当前位置特殊样式 */
            &.is-current {
              .point-icon {
                transform: scale(1.15);
                animation: currentPointPulse 2s ease-in-out infinite;
                z-index: 15;
                position: relative;

                /* 添加进度环 */
                &::before {
                  content: '';
                  position: absolute;
                  top: -4px;
                  left: -4px;
                  right: -4px;
                  bottom: -4px;
                  border: 2px solid transparent;
                  border-top: 2px solid #1890ff;
                  border-radius: 50%;
                  animation: progressRing 3s linear infinite;
                }
              }

              .point-name {
                color: #1890ff;
                font-weight: bold;
                text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
                animation: textGlow 2s ease-in-out infinite;
                position: relative;

                /* 添加巡更中提示 */
                &::after {
                  content: '巡更中';
                  position: absolute;
                  top: -15px;
                  left: 50%;
                  transform: translateX(-50%);
                  font-size: 8px;
                  color: #1890ff;
                  background: rgba(24, 144, 255, 0.1);
                  padding: 1px 4px;
                  border-radius: 3px;
                  white-space: nowrap;
                  animation: patrolText 2s ease-in-out infinite;
                }
              }

              .point-time {
                animation: textGlow 2s ease-in-out infinite;
              }

              // 隐藏当前位置的连接线，避免重复显示
              .point-connector {
                opacity: 0.3; // 降低透明度，减少视觉干扰
              }
            }

            /* 下一个目标特殊样式 */
            &.is-next {
              .point-icon {
                animation: nextPointBlink 1.5s ease-in-out infinite;
                position: relative;

                /* 添加等待指示器 */
                &::before {
                  content: '';
                  position: absolute;
                  top: -6px;
                  right: -6px;
                  width: 12px;
                  height: 12px;
                  background: #faad14;
                  border-radius: 50%;
                  animation: countdown 1s ease-in-out infinite;
                  box-shadow: 0 0 6px rgba(250, 173, 20, 0.8);
                }
              }

              .point-name {
                color: #faad14;
                font-weight: bold;
                text-shadow: 0 0 3px rgba(250, 173, 20, 0.8);
                animation: nextTextBlink 1.5s ease-in-out infinite;
                position: relative;

                /* 添加下一个提示 */
                &::after {
                  content: '下一个';
                  position: absolute;
                  top: -15px;
                  left: 50%;
                  transform: translateX(-50%);
                  font-size: 8px;
                  color: #faad14;
                  background: rgba(250, 173, 20, 0.1);
                  padding: 1px 4px;
                  border-radius: 3px;
                  white-space: nowrap;
                  animation: nextAlert 1.5s ease-in-out infinite;
                }
              }

              .point-time {
                animation: nextTextBlink 1.5s ease-in-out infinite;
              }
            }

            .point-icon {
              width: clamp(24px, 2.5vw, 32px);
              height: clamp(24px, 2.5vw, 32px);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: clamp(4px, 0.5vh, 6px);
              border: 2px solid rgba(255, 255, 255, 0.3);
              transition: all 0.3s ease;
              position: relative;
              z-index: 10;

              &.status-checked {
                background: linear-gradient(45deg, #52c41a, #73d13d);
                border-color: #52c41a;
                color: #ffffff;
                box-shadow: 0 0 8px rgba(82, 196, 26, 0.6);

                :deep(.anticon) {
                  color: #ffffff;
                  font-size: clamp(12px, 1.2vw, 16px);
                }
              }

              &.status-current {
                background: linear-gradient(45deg, #1890ff, #40a9ff);
                border-color: #1890ff;
                color: #ffffff;
                box-shadow: 0 0 12px rgba(24, 144, 255, 0.8);

                :deep(.anticon) {
                  color: #ffffff;
                  font-size: clamp(12px, 1.2vw, 16px);
                }
              }

              &.status-pending {
                background: linear-gradient(45deg, #faad14, #ffc53d);
                border-color: #faad14;
                color: #ffffff;
                box-shadow: 0 0 8px rgba(250, 173, 20, 0.6);

                :deep(.anticon) {
                  color: #ffffff;
                  font-size: clamp(12px, 1.2vw, 16px);
                }
              }

              &.status-missed {
                background: linear-gradient(45deg, #ff4d4f, #ff7875);
                border-color: #ff4d4f;
                color: #ffffff;
                box-shadow: 0 0 8px rgba(255, 77, 79, 0.6);

                :deep(.anticon) {
                  color: #ffffff;
                  font-size: clamp(12px, 1.2vw, 16px);
                }
              }
            }

            .point-info {
              text-align: center;
              min-height: clamp(25px, 3vh, 35px);
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              width: 100%;

              .point-name {
                font-size: clamp(10px, 1vw, 12px);
                color: #ffffff;
                margin-bottom: 2px;
                white-space: nowrap;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;

                @media (max-width: 767px) {
                  font-size: 9px;
                  margin-bottom: 1px;
                }
              }

              .point-time {
                font-size: clamp(9px, 0.9vw, 10px);
                color: rgba(0, 255, 255, 0.8);
                font-family: 'Courier New', monospace;
                font-weight: bold;
                white-space: nowrap;

                @media (max-width: 767px) {
                  font-size: 8px;
                }
              }
            }

            /* 连接线 - 采用PatrolDashboard.vue样式 */
            .point-connector {
              position: absolute;
              top: clamp(12px, 1.25vw, 16px); // 基于图标中心位置
              left: 50%; // 从当前点中心开始
              width: calc(100% - clamp(30px, 3vw, 40px)); // 缩短连接线，为箭头留出空间
              height: 3px; // 连接线高度
              z-index: 5; // 提高z-index
              transform: translateX(clamp(12px, 1.25vw, 16px)); // 从图标右边缘开始
              pointer-events: none; // 避免阻挡点击事件

              .connector-line {
                width: 100%; // 连接线占满整个容器
                height: 100%;
                background: linear-gradient(90deg,
                  rgba(0, 255, 255, 0.9) 0%,
                  rgba(0, 255, 255, 0.7) 50%,
                  rgba(0, 255, 255, 0.9) 100%
                );
                border-radius: 2px;
                position: relative;
                box-shadow: 0 0 6px rgba(0, 255, 255, 0.4);

                // 箭头连接在连接线末端，但不覆盖下一个点
                &::after {
                  content: '';
                  position: absolute;
                  top: 50%;
                  right: -8px; // 箭头稍微延伸，但不覆盖下一个点
                  width: 0;
                  height: 0;
                  border-left: 8px solid rgba(0, 255, 255, 0.9);
                  border-top: 4px solid transparent;
                  border-bottom: 4px solid transparent;
                  transform: translateY(-50%);
                  filter: drop-shadow(0 0 3px rgba(0, 255, 255, 0.8));
                  z-index: 10;
                }
              }
            }

            // 当前位置到下一个点的连接线 - 蓝色到黄色渐变
            &.is-current .point-connector {
              .connector-line {
                background: linear-gradient(90deg,
                  rgba(24, 144, 255, 0.9) 0%,   // 蓝色（当前位置）
                  rgba(24, 144, 255, 0.7) 30%,
                  rgba(250, 173, 20, 0.7) 70%,  // 黄色（下一个目标）
                  rgba(250, 173, 20, 0.9) 100%
                );
                box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);

                &::after {
                  border-left: 8px solid rgba(250, 173, 20, 0.9); // 箭头使用下一个点的颜色
                  filter: drop-shadow(0 0 3px rgba(250, 173, 20, 0.8));
                }
              }
            }

            // 已完成的连接线 - 绿色
            &.status-checked .point-connector {
              .connector-line {
                background: linear-gradient(90deg,
                  rgba(82, 196, 26, 0.9) 0%,
                  rgba(82, 196, 26, 0.7) 50%,
                  rgba(82, 196, 26, 0.9) 100%
                );
                box-shadow: 0 0 6px rgba(82, 196, 26, 0.4);

                &::after {
                  border-left: 8px solid rgba(82, 196, 26, 0.9);
                  filter: drop-shadow(0 0 3px rgba(82, 196, 26, 0.8));
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 巡更计划动画效果 */
@keyframes progressBar {
  0% { width: 0%; }
  100% { width: 100%; }
}

@keyframes currentPointPulse {
  0%, 100% {
    transform: scale(1.15);
    box-shadow: 0 0 12px rgba(24, 144, 255, 0.8);
  }
  50% {
    transform: scale(1.25);
    box-shadow: 0 0 20px rgba(24, 144, 255, 1);
  }
}

@keyframes progressRing {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 8px rgba(24, 144, 255, 1), 0 0 12px rgba(24, 144, 255, 0.8);
  }
}

@keyframes patrolText {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.05);
  }
}

@keyframes nextPointBlink {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes countdown {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes nextTextBlink {
  0%, 100% {
    color: #faad14;
    text-shadow: 0 0 3px rgba(250, 173, 20, 0.8);
  }
  50% {
    color: #ffc53d;
    text-shadow: 0 0 6px rgba(250, 173, 20, 1);
  }
}

@keyframes nextAlert {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .command-center {
    .command-header {
      padding: 15px 20px;

      .header-title .title-glow {
        font-size: 28px;
      }
    }

    .staff-section {
      padding: 15px 20px;

      .section-header {
        .carousel-controls {
          flex-direction: column;
          gap: 5px;

          .ant-btn {
            font-size: 11px;
            padding: 3px 8px;
          }
        }
      }

      .carousel-indicators {
        flex-direction: column;
        gap: 15px;
        margin-top: 20px;
        padding: 12px 15px;

        .carousel-info {
          order: 1;
          text-align: center;
        }

        .carousel-dots {
          order: 2;
          justify-content: center;

          .dot {
            width: 8px;
            height: 8px;
          }
        }

        .carousel-nav {
          order: 3;
          justify-content: center;
        }
      }

      .section-carousel {
        .section-group .subsections-container {
          grid-template-columns: 1fr;
          gap: 15px;

          .subsection-group .staff-cards {
            flex-wrap: wrap;
            gap: 8px;

            .staff-card {
              flex: 1;
              min-width: 100px;
              max-width: 150px;

              .staff-avatar {
                width: 60px;
                height: 60px;
              }

              .staff-info {
                .staff-name {
                  font-size: 13px;
                }

                .staff-card-id {
                  font-size: 11px;
                }

                .staff-status {
                  font-size: 10px;
                }
              }
            }
          }
        }
      }

      .subsection-carousel {
        .subsection-group-large {
          padding: 20px;

          .staff-cards-large {
            flex-direction: column;
            gap: 20px;

            .staff-card-large {
              max-width: none;

              .staff-avatar-large {
                width: 80px;
                height: 80px;
              }

              .staff-info-large {
                .staff-name {
                  font-size: 16px;
                }

                .staff-card-id {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
    }

    .patrol-plans-section {
      padding: 20px;

      .plans-container .plans-scroll {
        max-height: 400px;

        .plan-item {
          .plan-summary {
            flex-wrap: wrap;
            gap: 10px;

            .summary-item {
              flex: 1;
              min-width: 120px;
            }
          }

          .patrol-points {
            flex-wrap: wrap;

            .patrol-point {
              .point-icon {
                width: 28px;
                height: 28px;
              }

              .point-info .point-name {
                font-size: 9px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .command-center {
    .command-header {
      flex-direction: column;
      gap: 15px;
      text-align: center;

      .header-actions {
        justify-content: center;
      }
    }

    .staff-section {
      padding: 10px 15px;

      .section-header {
        justify-content: center;

        .carousel-controls {
          .ant-btn {
            font-size: 10px;
            padding: 2px 6px;
          }
        }
      }

      .carousel-indicators {
        padding: 8px 10px;
        margin-top: 15px;
        flex-direction: column;
        gap: 10px;

        .carousel-info {
          font-size: 12px;
          text-align: center;
          order: 1;
        }

        .carousel-dots {
          order: 2;
          justify-content: center;
          padding: 4px 8px;

          .dot {
            width: 6px;
            height: 6px;
          }
        }

        .carousel-nav {
          order: 3;
          justify-content: center;

          .ant-btn {
            font-size: 12px;
            padding: 3px 6px;
            min-width: 28px;
            height: 24px;

            .anticon {
              font-size: 12px;
            }
          }
        }
      }

      .section-carousel {
        .section-group {

          .subsections-container .subsection-group {
            padding: 15px;

            .subsection-name {
              font-size: 16px;
            }

            .staff-cards .staff-card {
              min-width: 90px;
              max-width: 120px;
              padding: 10px;

              .staff-avatar {
                width: 50px;
                height: 50px;
              }

              .staff-info {
                .staff-name {
                  font-size: 12px;
                }

                .staff-card-id {
                  font-size: 10px;
                }

                .staff-status {
                  font-size: 9px;
                }
              }
            }
          }
        }
      }

      .subsection-carousel {
        .subsection-group-large {
          padding: 15px;

          .subsection-header {
            .parent-section {
              font-size: 14px;
            }

            .subsection-name-large {
              font-size: 22px;
            }
          }

          .staff-cards-large .staff-card-large {
            padding: 15px;

            .staff-avatar-large {
              width: 70px;
              height: 70px;
            }

            .staff-info-large {
              .staff-name {
                font-size: 14px;
              }

              .staff-card-id {
                font-size: 12px;
              }

              .staff-status {
                font-size: 10px;
              }

              .staff-department {
                font-size: 9px;
              }
            }
          }
        }
      }
    }

    .patrol-plans-section .plans-container .plans-scroll .plan-item {
      .plan-summary {
        .summary-item {
          min-width: 100px;

          .label, .value {
            font-size: 11px;
          }
        }
      }

      .patrol-points .patrol-point {
        .point-icon {
          width: 24px;
          height: 24px;
        }

        .point-info .point-name {
          font-size: 8px;
        }
      }
    }
  }
}

/* 全屏模式下的样式优化 */
.fullscreen-mode {
  .command-header {
    padding: 25px 50px;

    .header-title .title-glow {
      font-size: 42px;
    }

    .header-actions .header-time {
      font-size: 20px;
    }
  }

  .staff-section {
    padding: 30px 50px;

    .section-header {
      margin-bottom: 25px;

      .carousel-controls {
        .ant-btn {
          font-size: 14px;
          // padding: 8px 18px;
          min-width: 80px;
        }
      }
    }

    .carousel-indicators {
      margin-top: 25px;
      padding: 12px 25px;

      .carousel-info {
        font-size: 16px;

        span {
          font-size: 16px;
        }
      }

      .carousel-dots {
        padding: 8px 12px;

        .dot {
          width: 10px;
          height: 10px;
        }
      }

      .carousel-nav .ant-btn {
        font-size: 16px;
        padding: 6px 12px;
        min-width: 36px;
        height: 32px;

        .anticon {
          font-size: 16px;
        }
      }
    }

    .section-carousel {
      .section-group {

        .subsections-container .subsection-group {
          padding: 25px;

          .subsection-name {
            font-size: 20px;
          }

          .staff-cards .staff-card {
            padding: 18px;
            max-width: 140px;

            .staff-avatar {
              width: 80px;
              height: 80px;
            }

            .staff-info {
              .staff-name {
                font-size: 16px;
              }

              .staff-card-id {
                font-size: 14px;
              }

              .staff-status {
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    .subsection-carousel {
      .subsection-group-large {
        padding: 40px;

        .subsection-header {
          .parent-section {
            font-size: 20px;
          }

          .subsection-name-large {
            font-size: 32px;
          }
        }

        .staff-cards-large .staff-card-large {
          padding: 30px;
          max-width: 250px;

          .staff-avatar-large {
            width: 120px;
            height: 120px;
          }

          .staff-info-large {
            .staff-name {
              font-size: 20px;
            }

            .staff-card-id {
              font-size: 16px;
            }

            .staff-status {
              font-size: 14px;
            }

            .staff-department {
              font-size: 13px;
            }
          }
        }
      }
    }
  }

  .patrol-plans-section {
    padding: 40px 50px;

    .plans-container .plans-scroll {
      max-height: 600px;

      .plan-item {
        padding: 18px;
        margin-bottom: 18px;

        .plan-summary {
          padding: 15px;

          .summary-item {
            .label {
              font-size: 14px;
            }

            .value {
              font-size: 15px;
            }

            .staff-info .staff-avatar-tiny {
              width: 24px;
              height: 24px;
            }
          }

          .plan-status-badge {
            padding: 6px 15px;
            font-size: 13px;
          }
        }

        .patrol-points .patrol-point {
          .point-icon {
            width: 36px;
            height: 36px;
          }

          .point-info {
            .point-name {
              font-size: 12px;
            }

            .point-time {
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
