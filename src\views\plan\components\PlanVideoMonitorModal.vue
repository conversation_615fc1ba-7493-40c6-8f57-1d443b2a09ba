<template>
  <div class="plan-video-monitor-modal">
    <!-- 工具栏 -->
    <div class="monitor-toolbar">
      <div class="toolbar-left">
        <h4>视频监控 ({{ videoList.length }})</h4>
        <a-tag color="blue">{{ planInfo?.name || '巡更计划' }}</a-tag>
      </div>
      <div class="toolbar-right">
        <a-button-group size="small">
          <a-button @click="setGridLayout(1)" :type="gridLayout === 1 ? 'primary' : 'default'">
            <AppstoreOutlined /> 1x1
          </a-button>
          <a-button @click="setGridLayout(4)" :type="gridLayout === 4 ? 'primary' : 'default'">
            <BorderOutlined /> 2x2
          </a-button>
          <a-button @click="setGridLayout(9)" :type="gridLayout === 9 ? 'primary' : 'default'">
            <BorderlessTableOutlined /> 3x3
          </a-button>
        </a-button-group>
        
        <a-divider type="vertical" />
        
        <a-button @click="startAllVideos" :loading="startingAll" size="small">
          <PlayCircleOutlined /> 全部播放
        </a-button>
        <a-button @click="stopAllVideos" size="small">
          <PauseCircleOutlined /> 全部停止
        </a-button>
        <a-button @click="refreshAllVideos" size="small">
          <ReloadOutlined /> 全部刷新
        </a-button>
      </div>
    </div>
    
    <!-- 视频网格 -->
    <div class="video-grid" :class="`grid-${getGridClass()}`">
      <div 
        v-for="(video, index) in displayVideoList" 
        :key="video.id || index"
        class="video-grid-item"
        :class="{ 'active': selectedVideoIndex === index }"
        @click="selectVideo(index)"
      >
        <VideoMonitorPlayerModal
          v-if="video.id"
          :ref="el => setVideoPlayerRef(el, index)"
          :plan-id="planInfo?.id || ''"
          :video-info="video"
          :auto-start="false"
          :show-controls="true"
          @error="onVideoError(index, $event)"
        />
        <div v-else class="empty-video-slot">
          <div class="empty-content">
            <VideoCameraOutlined />
            <p>空闲位置</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 视频详情侧边栏 -->
    <a-drawer
      v-model:open="detailVisible"
      title="视频详情"
      placement="right"
      width="300"
    >
      <div v-if="selectedVideo" class="video-detail">
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="名称">{{ selectedVideo.name }}</a-descriptions-item>
          <a-descriptions-item label="类型">
            <a-tag :color="getStreamTypeColor(selectedVideo.streamType)">
              {{ getStreamTypeText(selectedVideo.streamType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="RTSP地址">
            <a-typography-text copyable>{{ selectedVideo.videoUrl }}</a-typography-text>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getVideoStatus(selectedVideoIndex).color">
              {{ getVideoStatus(selectedVideoIndex).text }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <div class="video-actions">
          <a-space direction="vertical" style="width: 100%">
            <a-button block @click="toggleSelectedVideo">
              {{ getVideoStatus(selectedVideoIndex).playing ? '停止播放' : '开始播放' }}
            </a-button>
            <a-button block @click="refreshSelectedVideo">刷新视频</a-button>
            <a-button block @click="captureSelectedVideo">截图</a-button>
          </a-space>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  AppstoreOutlined,
  BorderOutlined,
  BorderlessTableOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  VideoCameraOutlined
} from '@ant-design/icons-vue';
import VideoMonitorPlayerModal from './VideoMonitorPlayerModal.vue';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  hlsUrl: string;
  streamType?: string;
}

interface PlanInfo {
  id: string;
  name: string;
}

interface Props {
  planInfo: PlanInfo;
  videoList: VideoInfo[];
  defaultGridLayout?: number;
}

const props = withDefaults(defineProps<Props>(), {
  defaultGridLayout: 4
});

// 响应式数据
const gridLayout = ref(props.defaultGridLayout);
const selectedVideoIndex = ref(-1);
const detailVisible = ref(false);
const startingAll = ref(false);

// 视频播放器引用
const videoPlayerRefs = ref<any[]>([]);

// 计算属性
const selectedVideo = computed(() => {
  return selectedVideoIndex.value >= 0 ? props.videoList[selectedVideoIndex.value] : null;
});

const displayVideoList = computed(() => {
  const list = [...props.videoList];
  const totalSlots = gridLayout.value;
  
  // 填充空位置
  while (list.length < totalSlots) {
    list.push({} as VideoInfo);
  }
  
  return list.slice(0, totalSlots);
});

// 组件卸载
onUnmounted(() => {
  stopAllVideos();
});

/**
 * 设置视频播放器引用
 */
function setVideoPlayerRef(el: any, index: number) {
  if (el) {
    videoPlayerRefs.value[index] = el;
  }
}

/**
 * 设置网格布局
 */
function setGridLayout(layout: number) {
  gridLayout.value = layout;
  selectedVideoIndex.value = -1;
}

/**
 * 获取网格CSS类名
 */
function getGridClass() {
  switch (gridLayout.value) {
    case 1: return '1x1';
    case 4: return '2x2';
    case 9: return '3x3';
    default: return '2x2';
  }
}

/**
 * 选择视频
 */
function selectVideo(index: number) {
  if (index < props.videoList.length) {
    selectedVideoIndex.value = index;
    detailVisible.value = true;
  }
}

/**
 * 开始所有视频
 */
async function startAllVideos() {
  if (props.videoList.length === 0) {
    message.warning('没有可播放的视频');
    return;
  }
  
  startingAll.value = true;
  
  try {
    const promises = videoPlayerRefs.value.map(player => {
      if (player && player.startVideo) {
        return player.startVideo();
      }
      return Promise.resolve();
    });
    
    await Promise.allSettled(promises);
    message.success('已启动所有视频');
  } catch (error) {
    message.error('启动视频失败');
  } finally {
    startingAll.value = false;
  }
}

/**
 * 停止所有视频
 */
function stopAllVideos() {
  videoPlayerRefs.value.forEach(player => {
    if (player && player.stopVideo) {
      player.stopVideo();
    }
  });
  message.info('已停止所有视频');
}

/**
 * 刷新所有视频
 */
function refreshAllVideos() {
  videoPlayerRefs.value.forEach(player => {
    if (player && player.refreshVideo) {
      player.refreshVideo();
    }
  });
  message.info('已刷新所有视频');
}

/**
 * 切换选中视频播放状态
 */
function toggleSelectedVideo() {
  const player = videoPlayerRefs.value[selectedVideoIndex.value];
  if (player) {
    if (player.isPlaying()) {
      player.stopVideo();
    } else {
      player.startVideo();
    }
  }
}

/**
 * 刷新选中视频
 */
function refreshSelectedVideo() {
  const player = videoPlayerRefs.value[selectedVideoIndex.value];
  if (player && player.refreshVideo) {
    player.refreshVideo();
  }
}

/**
 * 截图选中视频
 */
function captureSelectedVideo() {
  const player = videoPlayerRefs.value[selectedVideoIndex.value];
  if (player && player.captureFrame) {
    player.captureFrame();
  }
}

/**
 * 获取视频状态
 */
function getVideoStatus(index: number) {
  const player = videoPlayerRefs.value[index];
  if (!player) {
    return { playing: false, color: 'default', text: '未知' };
  }
  
  const playing = player.isPlaying ? player.isPlaying() : false;
  return {
    playing,
    color: playing ? 'green' : 'red',
    text: playing ? '播放中' : '已停止'
  };
}

/**
 * 获取流类型颜色
 */
function getStreamTypeColor(type?: string) {
  switch (type) {
    case 'playback': return 'blue';
    case 'preview': return 'green';
    default: return 'default';
  }
}

/**
 * 获取流类型文本
 */
function getStreamTypeText(type?: string) {
  switch (type) {
    case 'playback': return '回放流';
    case 'preview': return '预览流';
    default: return '未知';
  }
}

/**
 * 视频错误处理
 */
function onVideoError(index: number, error: any) {
  console.error(`视频 ${index + 1} 错误:`, error);
  message.error(`视频 ${props.videoList[index]?.name || index + 1} 播放错误`);
}
</script>

<style scoped>
.plan-video-monitor-modal {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-left h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-grid {
  flex: 1;
  display: grid;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  overflow: auto;
  min-height: 300px;
}

.video-grid.grid-1x1 {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.video-grid.grid-2x2 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.video-grid.grid-3x3 {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

.video-grid-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-height: 120px;
}

.video-grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-grid-item.active {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.empty-video-slot {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
}

.empty-content {
  text-align: center;
  color: #999;
}

.empty-content .anticon {
  font-size: 24px;
  margin-bottom: 8px;
}

.video-detail {
  padding: 16px 0;
}

.video-actions {
  margin-top: 16px;
}
</style>
