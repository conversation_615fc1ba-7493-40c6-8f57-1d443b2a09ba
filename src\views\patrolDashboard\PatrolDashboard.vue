<template>
  <div class="patrol-dashboard" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 权限检查 - 如果用户没有管理路线权限，显示空状态 -->
    <div v-if="!hasPermission && !isLoading" class="no-permission-container">
      <a-empty
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
        description="您暂无管理路线权限，无法查看巡更数据"
      >
        <template #image>
          <div class="empty-icon">
            <Icon icon="ant-design:lock-outlined" size="64" />
          </div>
        </template>
      </a-empty>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <a-spin size="large" tip="正在检查权限...">
        <div class="loading-content"></div>
      </a-spin>
    </div>

    <!-- 主要内容 - 只有在有权限时才显示 -->
    <div v-if="hasPermission && !isLoading" class="dashboard-content">
      <!-- 分监区名称标题 -->
      <div class="dashboard-header">
        <div class="header-title">
          <div class="title-glow">{{ sectionName }}</div>
          <!-- <div class="subtitle">分监区可视化数据大屏</div> -->
        </div>
        <div class="header-actions">
          <div class="header-time">{{ currentTime }}</div>
          <a-button
            v-if="!isFullscreen"
            type="primary"
            class="fullscreen-btn"
            @click="toggleFullscreen"
            :icon="h(FullscreenOutlined)"
          >
            全屏显示
          </a-button>
          <a-button
            v-else
            type="default"
            class="exit-fullscreen-btn"
            @click="exitFullscreen"
            :icon="h(FullscreenExitOutlined)"
          >
            退出全屏
          </a-button>
        </div>
      </div>

    <!-- 今日巡更人员信息 -->
    <div class="patrol-staff-section">
      <!-- <div class="section-title">
        <div class="title-line"></div>
        <span>今日巡更人员</span>
        <div class="title-line"></div>
      </div> -->
      <div class="staff-cards">
        <div 
          v-for="(staff, index) in todayStaff" 
          :key="staff.id"
          class="staff-card"
          :class="{ 'active': staff.online }"
        >
          <div class="staff-avatar">
            <img :src="getFileAccessHttpUrl(staff.patrolUserImage) || defaultAvatar" :alt="staff.patrolUserName" />
            <div class="status-indicator" :class="staff.status"></div>
          </div>
          <div class="staff-info">
            <div class="staff-name">{{ staff.patrolUserName }}</div>
            <div class="staff-card-id">卡号: {{ staff.num }}</div>
            <div class="staff-status">{{ staff.online ? "巡更中" : "离线" }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时巡更计划状态 -->
    <div class="patrol-plans-section">
      <div class="section-title">
        <div class="title-line"></div>
        <span>实时巡更记录</span>

        <div class="title-line"></div>
      </div>
      
      <div class="plans-container">
        <div class="plans-scroll" ref="plansScrollRef">
          <div
            v-for="plan in sortedPlans"
            :key="plan.id"
            class="plan-item"
            :class="getPlanStatusClass(plan.status)"
          >
            <!-- 计划基本信息 - 一行显示 -->
            <div class="plan-summary">
              <div class="summary-item">
                <span class="label">计划:</span>
                <span class="value">{{ plan.name || '巡更计划' }}</span>
              </div>
              <!-- <div class="summary-item">
                <span class="label">监区:</span>
                <span class="value">{{ plan.lineName }}</span>
              </div> -->
              <div class="summary-item">
                <span class="label">巡更者:</span>
                <div class="staff-info">
                  <img :src="getFileAccessHttpUrl(plan.patrolUserImage) || defaultAvatar" :alt="plan.patrolUserName" class="staff-avatar-tiny" />
                  <span class="value">{{ plan.patrolUserName || '未知' }}</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="label">开始:</span>
                <span class="value">{{ plan.startTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.endTime">
                <span class="label">结束:</span>
                <span class="value">{{ plan.endTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.status === '2' && plan.patrolDuration">
                <span class="label">用时:</span>
                <span class="value">{{ plan.patrolDuration }}</span>
              </div>
              <div class="plan-status-badge" :class="getPlanStatusClass(plan.status)">
                {{ getStatusText(plan.status) }}
              </div>
            </div>

            <!-- 巡更点状态 - 铺满显示 -->
            <div class="patrol-points">
              <div
                v-for="(point, index) in plan.planCardList"
                :key="point.id"
                class="patrol-point"
                :class="[
                  `status-${getStatusClass(point.status)}`,
                  {
                    'is-current': String(plan.status) === '1' && Number(point.status) === 1,
                    'is-next': String(plan.status) === '1' && Number(point.status) === 0 && index === getCurrentPointIndex(plan) + 1
                  }
                ]"
                :style="{ width: `calc(${100 / (plan.planCardList?.length || plan.points?.length || 1)}% - ${((plan.planCardList?.length || plan.points?.length || 1) - 1) * 8 / (plan.planCardList?.length || plan.points?.length || 1)}px)` }"
              >
                <div class="point-icon" :class="`icon-${getStatusClass(point.status)}`">
                  <Icon :icon="getPointIcon(point.status)" />
                </div>
                <div class="point-info">
                  <div class="point-name">{{ point.cardName }}</div>
                  <div class="point-time" v-if="point.time">
                    {{ point.time }}
                  </div>
                </div>
                <!-- 连接线 -->
                <div v-if="index < plan.planCardList.length - 1" class="point-connector">
                  <div class="connector-line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, h } from 'vue';
import { Empty } from 'ant-design-vue';
import { getPatrolDashboardData, generateMockData, UserManagedLines, type StaffInfo, type PatrolPlan } from './PatrolDashboard.api';
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue';
import { Icon } from '/@/components/Icon';
import dayjs from 'dayjs';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

// 响应式数据
const sectionName = ref('第一分监区');
const currentTime = ref('');
const todayStaff = ref<StaffInfo[]>([]);
const patrolPlans = ref<PatrolPlan[]>([]);
const plansScrollRef = ref();
const isFullscreen = ref(false);

// 权限相关状态
const hasPermission = ref(false);
const isLoading = ref(true);

// 默认头像
const defaultAvatar = '/src/assets/images/ai/avatar.jpg';

// 定时器
let timeTimer: NodeJS.Timeout;
let dataTimer: NodeJS.Timeout;

// 计算属性 - 排序后的计划
const sortedPlans = computed(() => {
  const plans = [...patrolPlans.value];
  return plans.sort((a, b) => {
    const statusOrder = { 'in-progress': 0, 'pending': 1, 'completed': 2, 'missed': 3 };
    return statusOrder[a.status] - statusOrder[b.status];
  });
});

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
};

// 模拟实时巡更进度更新
const updatePatrolProgress = () => {
  // 随机更新巡更点状态（模拟实时巡更）
  patrolPlans.value.forEach(plan => {
    if (plan.status === 'in-progress') {
      plan.points.forEach((point, index) => {
        // 模拟巡更进度 - 5%概率更新状态
        if (Math.random() < 0.05) {
          if (point.status === 'pending' && index > 0) {
            // 检查前一个点是否已完成
            const prevPoint = plan.points[index - 1];
            if (prevPoint.status === 'checked' || prevPoint.status === 'current') {
              // 将当前点设为正在巡更
              point.status = 'current';
              // 将之前的current状态改为checked
              plan.points.forEach((p, i) => {
                if (i < index && p.status === 'current') {
                  p.status = 'checked';
                  p.checkTime = dayjs().format('HH:mm');
                }
              });
            }
          } else if (point.status === 'current' && Math.random() < 0.3) {
            // 30%概率完成当前巡更点
            point.status = 'checked';
            point.checkTime = dayjs().format('HH:mm');

            // 设置下一个点为待巡更状态
            if (index < plan.points.length - 1) {
              const nextPoint = plan.points[index + 1];
              if (nextPoint.status === 'pending') {
                // 有概率直接开始下一个点
                if (Math.random() < 0.5) {
                  nextPoint.status = 'current';
                }
              }
            }
          }
        }
      });
    }
  });
};

// 获取状态文本
const getStatusText = (status: string | number) => {
  // 先转换为字符串进行统一处理
  const statusStr = String(status);

  const statusMap = {
    // 人员状态
    'online': '在线',
    'offline': '离线',
    'on-duty': '值班中',
    // 兼容旧状态
    'missed': '漏巡',
    'checked': '已巡更',
    'current': '当前位置',
    'pending': '待巡',
    'in-progress': '进行中',
    'completed': '已完成'
  };

  // 对于数字状态，需要根据上下文判断是计划状态还是打卡点状态
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);

    // 这里我们需要一个更智能的判断方式
    // 可以通过调用栈或者传入额外参数来区分
    // 暂时使用通用的状态文本
    switch (numStatus) {
      case 0: return '待巡';
      case 1: return '进行中';  // 对于计划状态，1表示进行中
      case 2: return '已完成'; // 对于计划状态，2表示已完成
    }
  }

  return statusMap[statusStr] || statusStr;
};

// 专门用于获取打卡点状态文本
const getPointStatusText = (status: string | number) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return '待巡';
      case 1: return '已巡更';
      case 2: return '漏巡';
    }
  }
  return getStatusText(status);
};

// 获取状态对应的CSS类名（用于巡更点）
const getStatusClass = (status: string | number) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return 'pending';     // 待巡
      case 1: return 'checked';     // 已巡更
      case 2: return 'missed';      // 漏巡
    }
  }
  // 兼容旧状态
  return String(status);
};

// 获取计划状态对应的CSS类名
const getPlanStatusClass = (status: string | number) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return 'pending';       // 待巡
      case 1: return 'in-progress';   // 进行中
      case 2: return 'completed';     // 已完成
    }
  }

  // 兼容旧状态
  const statusStr = String(status);
  switch (statusStr) {
    case 'pending': return 'pending';
    case 'in-progress': return 'in-progress';
    case 'completed': return 'completed';
    case 'missed': return 'missed';
    default: return 'pending';
  }
};

// 获取巡更点图标
const getPointIcon = (status: number | string) => {
  const iconMap = {
    0: 'ant-design:clock-circle-outlined',      // 待巡
    1: 'ant-design:check-circle-filled',        // 已巡更
    2: 'ant-design:close-circle-filled',        // 漏巡
    'pending': 'ant-design:clock-circle-outlined',   // 兼容旧状态
    'checked': 'ant-design:check-circle-filled',     // 兼容旧状态
    'current': 'ant-design:environment-filled',      // 兼容旧状态
    'missed': 'ant-design:close-circle-filled'       // 兼容旧状态
  };
  return iconMap[status] || 'ant-design:clock-circle-outlined';
};

// 获取当前巡更点索引
const getCurrentPointIndex = (plan: any) => {
  // 优先查找数字状态1（已巡更）的最后一个点
  const points = plan.planCardList || plan.points || [];
  let lastCheckedIndex = -1;

  for (let i = 0; i < points.length; i++) {
    const point = points[i];
    if (Number(point.status) === 1) {
      lastCheckedIndex = i;
    }
  }

  return lastCheckedIndex;
};

// 全屏功能
const toggleFullscreen = () => {
  const element = document.documentElement;
  if (!document.fullscreenElement) {
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if ((element as any).webkitRequestFullscreen) {
      (element as any).webkitRequestFullscreen();
    } else if ((element as any).msRequestFullscreen) {
      (element as any).msRequestFullscreen();
    }
    isFullscreen.value = true;
  }
};

const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if ((document as any).webkitExitFullscreen) {
    (document as any).webkitExitFullscreen();
  } else if ((document as any).msExitFullscreen) {
    (document as any).msExitFullscreen();
  }
  isFullscreen.value = false;
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 检查用户权限
const checkUserPermission = async () => {
  try {
    isLoading.value = true;
    const response = await UserManagedLines({});

    // 根据后端返回结果判断权限
    if (response && response.success !== false && response.result == true) {
      hasPermission.value = true;
      // 如果有权限，加载数据
      await loadData();
    } else {
      hasPermission.value = false;
      console.log('用户没有管理路线权限');
    }
  } catch (error) {
    console.error('检查权限失败:', error);
    hasPermission.value = false;
  } finally {
    isLoading.value = false;
  }
};

// 加载数据
const loadData = async () => {
  if (!hasPermission.value) {
    return;
  }

  try {
    const data = await getPatrolDashboardData();
    
    sectionName.value = data.line.name || '第一分监区';
    todayStaff.value = data.cards || [];
    patrolPlans.value = data.plan || [];
  } catch (error) {
    console.error('加载数据失败:', error);
    // 使用模拟数据
    const mockData = generateMockData();
    sectionName.value = mockData.sectionName;
    todayStaff.value = mockData.todayStaff;
    patrolPlans.value = mockData.patrolPlans;
  }
};



// 组件挂载
onMounted(async () => {
  updateCurrentTime();
  timeTimer = setInterval(updateCurrentTime, 1000);

  // 首先检查用户权限
  await checkUserPermission();

  // 只有在有权限时才启动定时器
  if (hasPermission.value) {
    dataTimer = setInterval(loadData, 30000); // 30秒刷新一次数据

    // 启动实时巡更进度更新
    const progressTimer = setInterval(updatePatrolProgress, 3000); // 每3秒更新一次巡更进度

    // 清理定时器
    onUnmounted(() => {
      clearInterval(progressTimer);
    });
  }

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('msfullscreenchange', handleFullscreenChange);
});

// 组件卸载
onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer);
  if (dataTimer) clearInterval(dataTimer);

  // 移除全屏监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('msfullscreenchange', handleFullscreenChange);
});
</script>

<style lang="less" scoped>
.patrol-dashboard {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0a0e27 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  position: relative;

  /* 权限检查相关样式 */
  .no-permission-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80vh;

    .empty-icon {
      color: rgba(255, 255, 255, 0.3);
      margin-bottom: 20px;
    }

    :deep(.ant-empty) {
      color: rgba(255, 255, 255, 0.8);

      .ant-empty-description {
        color: rgba(255, 255, 255, 0.6);
        font-size: 16px;
      }
    }
  }

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80vh;

    .loading-content {
      width: 200px;
      height: 100px;
    }

    :deep(.ant-spin) {
      .ant-spin-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
      }

      .ant-spin-dot {
        .ant-spin-dot-item {
          background-color: #00ffff;
        }
      }
    }
  }

  .dashboard-content {
    width: 100%;
    height: 100%;
  }

  &.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(255, 0, 150, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  // 响应式设计
  @media (max-width: 1920px) {
    font-size: 14px;
  }

  @media (max-width: 1440px) {
    font-size: 12px;
  }

  @media (max-width: 1024px) {
    font-size: 10px;
  }
}

// 头部标题区域
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2vh 4vw;
  border-bottom: 2px solid rgba(0, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);



  .header-title {
    .title-glow {
      font-size: clamp(24px, 4vw, 48px);
      font-weight: bold;
      background: linear-gradient(45deg, #00ffff, #0099ff, #00ffff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      margin-bottom: 8px;


    }

    .subtitle {
      font-size: clamp(12px, 1.5vw, 18px);
      color: rgba(255, 255, 255, 0.7);
      letter-spacing: 2px;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 20px;

    .header-time {
      font-size: clamp(16px, 2vw, 24px);
      color: #00ffff;
      font-family: 'Courier New', monospace;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    }

    .fullscreen-btn, .exit-fullscreen-btn {
      background: linear-gradient(45deg, #00ffff, #0099ff);
      border: none;
      color: #ffffff;
      font-weight: bold;
      box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(0, 255, 255, 0.5);
      }
    }

    .exit-fullscreen-btn {
      background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
      box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);

      &:hover {
        box-shadow: 0 5px 20px rgba(255, 107, 107, 0.5);
      }
    }
  }
}

// 人员信息区域
.patrol-staff-section {
  padding: 3vh 4vw;
  transition: all 0.3s ease;



  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 3vh;



    .title-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    span {
      margin: 0 20px;
      font-size: clamp(16px, 2vw, 24px);
      font-weight: bold;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    }
  }

  .staff-cards {
    display: flex;
    justify-content: center;
    gap: 2vw;
    flex-wrap: wrap;



    .staff-card {
      width: clamp(200px, 18vw, 280px);
      padding: 25px;
      background: rgba(0, 0, 0, 0.4);
      border: 2px solid rgba(0, 255, 255, 0.3);
      border-radius: 15px;
      text-align: center;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);



      &.active {
        border-color: #00ff88;
        box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);

        .status-indicator {
          background: #00ff88;
          box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
        }
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
      }

      .staff-avatar {
        position: relative;
        width: clamp(80px, 8vw, 120px);
        height: clamp(80px, 8vw, 120px);
        margin: 0 auto 15px;



        img {
          width: 100%;
          height: 100%;
          border-radius: 8px; // 改为正方形，圆角8px
          border: 3px solid rgba(0, 255, 255, 0.5);
          object-fit: cover;
          box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }

        .status-indicator {
          position: absolute;
          bottom: -5px;
          right: -5px;
          width: clamp(16px, 2vw, 24px);
          height: clamp(16px, 2vw, 24px);
          border-radius: 50%;
          background: #ff4444;
          border: 2px solid #ffffff;

          &.online {
            background: #00ff88;
          }

          &.on-duty {
            background: #00ff88;
            animation: pulse 2s infinite;
          }
        }
      }

      .staff-info {
        .staff-name {
          font-size: clamp(14px, 1.5vw, 18px);
          font-weight: bold;
          margin-bottom: 8px;
          color: #ffffff;


        }

        .staff-card-id {
          font-size: clamp(12px, 1.2vw, 14px);
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 8px;


        }

        .staff-status {
          font-size: clamp(12px, 1.2vw, 14px);
          color: #00ffff;
          font-weight: bold;


        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

// 巡更计划区域
.patrol-plans-section {
  padding: 0 4vw 2vh;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;



  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 1.5vh;
    position: relative;



    .title-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    span {
      margin: 0 20px;
      font-size: clamp(16px, 2vw, 24px);
      font-weight: bold;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    }


  }

  .plans-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(1080px - 330px);
    max-height: calc(1080px - 330px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  .plans-scroll {
    display: flex;
    flex-direction: column;
    gap: clamp(8px, 1vh, 12px); // 普通模式下的间距
    // 普通模式下不限制高度和滚动
    padding-bottom: clamp(20px, 3vh, 40px); // 普通模式下的底部内边距
    flex: 1;
  }

  // 全屏模式下的优化布局
  &.fullscreen-mode {
    .dashboard-content {
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
    }

    .dashboard-header {
      flex-shrink: 0; // 防止头部被压缩
      height: auto;
    }

    .patrol-staff-section {
      flex-shrink: 0; // 防止人员区域被压缩
      height: auto;
    }

    .patrol-plans-section {
      flex: 1; // 占用剩余空间
      display: flex;
      flex-direction: column;
      overflow: hidden;
      min-height: 0; // 允许flex子项缩小
      height: calc(1080px - 350px); // 调整高度计算，为更大的人员区域预留空间
    }

    .section-title {
      flex-shrink: 0; // 防止标题被压缩
      margin-bottom: clamp(8px, 1vh, 15px); // 减少底部间距
    }

    .plans-container {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      min-height: 0; // 允许flex子项缩小
      height: calc(1080px - 470px); // 调整高度，为更大的人员区域预留空间
      max-height: calc(1080px - 470px);
    }

    .plans-scroll {
      flex: 1;
      overflow-y: auto; // 启用垂直滚动
      overflow-x: hidden; // 禁用水平滚动
      padding-right: 12px; // 为滚动条留出空间
      padding-bottom: clamp(20px, 2vh, 30px); // 底部空间
      gap: clamp(8px, 1vh, 12px); // 保持合适的间距
      padding-top: clamp(8px, 1vh, 12px); // 顶部空间
     

      // 全屏时的滚动条样式
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, #00ffff, #0096ff);
        border-radius: 4px;
        box-shadow: 0 0 6px rgba(0, 255, 255, 0.3);

        &:hover {
          background: linear-gradient(45deg, #0096ff, #00ffff);
          box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }
      }
    }
  }

  .plan-item {
        padding: clamp(12px, 1.5vw, 18px); // 普通模式下的内边距
        background: rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 12px; // 普通模式下的圆角
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        flex-shrink: 0; // 防止列表项被压缩
        margin-bottom: clamp(12px, 1.5vh, 20px); // 普通模式下的底部间距



        &.in-progress {
          border-color: #1890ff;
          box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);

          .plan-status-badge {
            background: linear-gradient(45deg, #1890ff, #40a9ff);
          }
        }

        &.pending {
          border-color: #faad14;

          .plan-status-badge {
            background: linear-gradient(45deg, #faad14, #ffc53d);
          }
        }

        &.completed {
          border-color: #52c41a;

          .plan-status-badge {
            background: linear-gradient(45deg, #52c41a, #73d13d);
          }
        }

        &.missed {
          border-color: #ff4d4f;

          .plan-status-badge {
            background: linear-gradient(45deg, #ff4d4f, #ff7875);
          }
        }

        &:hover {
          transform: translateX(5px); // 减小移动距离
          box-shadow: 0 3px 15px rgba(0, 255, 255, 0.2);
        }
      }
    }
  

// 全屏模式下的样式
.fullscreen-mode {

  .dashboard-header {
    padding: 1vh 2vw; // 减少内边距，释放更多空间

    .title-glow {
      font-size: clamp(20px, 3vw, 36px);
      margin-bottom: 4px;
    }
  }

  .patrol-staff-section {
    padding: 0.8vh 2vw; // 增加内边距，给人员区域更多空间
    max-height: 25vh; // 增加人员区域最大高度
    overflow: hidden; // 防止人员区域过高

    .section-title {
      margin-bottom: 0.8vh; // 增加底部间距
    }

    .staff-cards {
      gap: 1.2vw; // 增加卡片间距，让布局更舒适

      .staff-card {
        width: clamp(160px, 15vw, 220px); // 增大卡片宽度
        padding: 12px; // 增加内边距
        border-radius: 8px; // 增大圆角

        .staff-avatar {
          width: clamp(65px, 7vw, 85px); // 增大头像尺寸
          height: clamp(65px, 7vw, 85px);
          margin: 0 auto 10px; // 增加底部间距
        }

        .staff-info {
          .staff-name {
            font-size: clamp(14px, 1.4vw, 16px); // 增大字体
            margin-bottom: 4px; // 增加间距
            font-weight: bold; // 加粗姓名
          }

          .staff-card-id {
            font-size: clamp(12px, 1.2vw, 14px); // 增大字体
            margin-bottom: 4px; // 增加间距
          }

          .staff-status {
            font-size: clamp(12px, 1.2vw, 14px); // 增大状态文字
            font-weight: 500; // 稍微加粗状态文字
          }
        }
      }
    }
  }

  .patrol-plans-section {
    padding: 0 2vw 0.5vh; // 进一步减少内边距

    .section-title {
      margin-bottom: 0.3vh; // 进一步减少底部间距

      span {
        font-size: clamp(18px, 2.2vw, 24px); // 减小标题字体
      }
    }

    .plan-item {
      padding: clamp(8px, 1vw, 12px); // 进一步减少内边距
      border-radius: 6px; // 进一步减少圆角
      margin-bottom: clamp(6px, 0.8vh, 10px); // 优化间距
    }
  }

  .plan-summary {
    margin-bottom: clamp(6px, 0.8vh, 10px); // 全屏时减少底部间距
    padding: clamp(8px, 1vh, 12px); // 全屏时减少内边距
    border-radius: 6px; // 全屏时减少圆角

    &::before {
      height: 2px; // 全屏时进度条更细
    }

    .label {
      font-size: clamp(11px, 1.1vw, 13px); // 全屏时适度减小字体
    }

    .value {
      font-size: clamp(11px, 1.1vw, 13px); // 全屏时适度减小字体
    }
  }

  .point-icon {
    width: clamp(22px, 2.2vw, 30px); // 全屏时压缩图标尺寸
    height: clamp(22px, 2.2vw, 30px);
    margin-bottom: clamp(4px, 0.6vh, 8px); // 全屏时减少底部间距
  }

  .point-name {
    font-size: clamp(9px, 0.9vw, 11px); // 全屏时适度减小字体
    margin-bottom: 1px; // 全屏时减少间距
  }

  .point-time {
    font-size: clamp(8px, 0.8vw, 10px); // 全屏时适度减小字体
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
  }
}

// 计划摘要 - 一行显示
.plan-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: clamp(10px, 1.2vh, 15px); // 普通模式下的间距
  padding: clamp(10px, 1.2vh, 15px); // 普通模式下的内边距
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px; // 普通模式下的圆角
  border: 1px solid rgba(0, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  // 添加实时进度条
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px; // 普通模式下的进度条高度
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    animation: progressBar 10s linear infinite;
    box-shadow: 0 0 6px rgba(24, 144, 255, 0.6);
  }



  .summary-item {
    display: flex;
    align-items: center;
    gap: 6px;

    .label {
      font-size: clamp(12px, 1.2vw, 14px); // 普通模式下的字体
      color: rgba(0, 255, 255, 0.8);
      font-weight: bold;
      white-space: nowrap;


    }

    .value {
      font-size: clamp(12px, 1.2vw, 14px); // 普通模式下的字体
      color: #ffffff;
      font-weight: normal;
      white-space: nowrap;


    }

    .staff-info {
      display: flex;
      align-items: center;
      gap: 6px;

      .staff-avatar-tiny {
        width: clamp(18px, 1.8vw, 24px); // 增大头像
        height: clamp(18px, 1.8vw, 24px); // 增大头像
        border-radius: 4px;
        border: 1px solid rgba(0, 255, 255, 0.5);
        object-fit: cover;
      }
    }
  }

  .plan-status-badge {
    padding: clamp(5px, 0.8vw, 8px) clamp(12px, 1.5vw, 16px); // 增大内边距
    border-radius: 12px;
    font-size: clamp(11px, 1.1vw, 13px); // 增大字体
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
    white-space: nowrap;
    flex-shrink: 0;
  }
}

.patrol-points {
  display: flex;
  align-items: center;
  justify-content: space-between; // 均匀分布
  margin-bottom: clamp(8px, 1vh, 12px);
  padding: clamp(8px, 1vh, 12px) 0;
  position: relative;

  .patrol-point {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1; // 平均分配宽度
    max-width: none; // 移除最大宽度限制
    min-width: 0; // 允许收缩

    // 当前位置特殊样式
    &.is-current {
      .point-icon {
        transform: scale(1.15);
        animation: currentPointPulse 2s ease-in-out infinite;
        z-index: 15; // 确保当前位置图标在连接线之上
        position: relative;

        // 添加实时巡更指示器
        &::before {
          content: '';
          position: absolute;
          top: -8px;
          right: -8px;
          width: 12px;
          height: 12px;
          background: #1890ff;
          border-radius: 50%;
          animation: patrolIndicator 1s ease-in-out infinite;
          box-shadow: 0 0 8px rgba(24, 144, 255, 0.8);
        }

        // 添加巡更进度环
        &::after {
          content: '';
          position: absolute;
          top: -4px;
          left: -4px;
          right: -4px;
          bottom: -4px;
          border: 2px solid transparent;
          border-top: 2px solid #1890ff;
          border-radius: 50%;
          animation: progressRing 3s linear infinite;
        }
      }

      .point-name {
        color: #1890ff;
        font-weight: bold;
        text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
        animation: textGlow 2s ease-in-out infinite;
        position: relative;

        // 添加"正在巡更"提示
        &::after {
          content: '正在巡更...';
          position: absolute;
          top: -20px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 8px;
          color: #1890ff;
          white-space: nowrap;
          animation: patrolText 2s ease-in-out infinite;
        }
      }

      .point-time {
        animation: textGlow 2s ease-in-out infinite;
      }

      // 隐藏当前位置的连接线，避免重复显示
      .point-connector {
        opacity: 0.3; // 降低透明度，减少视觉干扰
      }
    }

    // 下一个目标特殊样式
    &.is-next {
      .point-icon {
        animation: nextPointBlink 1.5s ease-in-out infinite;
        position: relative;

        // 添加等待指示器
        &::before {
          content: '';
          position: absolute;
          top: -6px;
          left: -6px;
          right: -6px;
          bottom: -6px;
          border: 2px dashed #faad14;
          border-radius: 50%;
          animation: waitingRing 4s linear infinite;
          opacity: 0.7;
        }

        // 添加倒计时效果
        &::after {
          content: '';
          position: absolute;
          top: -10px;
          right: -10px;
          width: 8px;
          height: 8px;
          background: #faad14;
          border-radius: 50%;
          animation: countdown 1s ease-in-out infinite;
          box-shadow: 0 0 6px rgba(250, 173, 20, 0.8);
        }
      }

      .point-name {
        color: #faad14;
        font-weight: bold;
        text-shadow: 0 0 3px rgba(250, 173, 20, 0.8);
        animation: nextTextBlink 1.5s ease-in-out infinite;
        position: relative;

        // 添加"即将到达"提示
        &::after {
          content: '即将到达';
          position: absolute;
          top: -18px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 8px;
          color: #faad14;
          white-space: nowrap;
          animation: nextAlert 1.5s ease-in-out infinite;
        }
      }

      .point-time {
        animation: nextTextBlink 1.5s ease-in-out infinite;
      }
    }

    .point-icon {
      width: clamp(24px, 2.5vw, 32px); // 普通模式下的尺寸
      height: clamp(24px, 2.5vw, 32px);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: clamp(5px, 0.8vh, 8px); // 普通模式下的间距
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;



      // 已巡更状态 - 绿色
      &.icon-checked {
        background: linear-gradient(45deg, #52c41a, #73d13d);
        box-shadow: 0 0 10px rgba(82, 196, 26, 0.6);
        border: 1px solid #ffffff;

        :deep(.anticon) {
          color: #ffffff !important;
          font-size: clamp(10px, 1.2vw, 14px) !important;
        }
      }

      // 当前位置状态 - 蓝色
      &.icon-current {
        background: linear-gradient(45deg, #1890ff, #40a9ff);
        box-shadow: 0 0 15px rgba(24, 144, 255, 1);
        border: 2px solid #ffffff;

        :deep(.anticon) {
          color: #ffffff !important;
          font-size: clamp(12px, 1.4vw, 16px) !important;
        }
      }

      // 待巡更状态 - 黄色
      &.icon-pending {
        background: linear-gradient(45deg, #faad14, #ffc53d);
        border: 1px solid #ffffff;
        box-shadow: 0 0 10px rgba(250, 173, 20, 0.6);

        :deep(.anticon) {
          color: #ffffff !important;
          font-size: clamp(9px, 1.1vw, 12px) !important; // 增大字体
        }
      }

      // 漏巡状态 - 红色
      &.icon-missed {
        background: linear-gradient(45deg, #ff4d4f, #ff7875);
        box-shadow: 0 0 10px rgba(255, 77, 79, 0.6);
        border: 1px solid #ffffff;

        :deep(.anticon) {
          color: #ffffff !important;
          font-size: clamp(10px, 1.2vw, 14px) !important; // 增大字体
        }
      }
    }

    .point-info {
      text-align: center;
      min-height: clamp(25px, 3vh, 35px); // 增加高度
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      width: 100%; // 占满父容器宽度

      .point-name {
        font-size: clamp(10px, 1vw, 12px); // 普通模式下的字体
        color: #ffffff;
        margin-bottom: 2px; // 普通模式下的间距
        white-space: nowrap;
        width: 100%; // 占满宽度
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.2;


      }

      .point-time {
        font-size: clamp(9px, 0.9vw, 10px); // 普通模式下的字体
        color: rgba(0, 255, 255, 0.8);
        font-family: 'Courier New', monospace;
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;


      }
    }

    // 连接线 - 箭头刚好连接到下一个打卡点
    .point-connector {
      position: absolute;
      top: clamp(12px, 1.25vw, 16px); // 基于图标中心位置
      left: 50%; // 从当前点中心开始
      width: calc(100% - clamp(30px, 3vw, 40px)); // 缩短连接线，为箭头留出空间
      height: 3px; // 连接线高度
      z-index: 5; // 提高z-index
      transform: translateX(clamp(12px, 1.25vw, 16px)); // 从图标右边缘开始
      pointer-events: none; // 避免阻挡点击事件

      .connector-line {
        width: 100%; // 连接线占满整个容器
        height: 100%;
        background: linear-gradient(90deg,
          rgba(0, 255, 255, 0.9) 0%,
          rgba(0, 255, 255, 0.7) 50%,
          rgba(0, 255, 255, 0.9) 100%
        );
        border-radius: 2px;
        position: relative;
        box-shadow: 0 0 6px rgba(0, 255, 255, 0.4);

        // 箭头连接在连接线末端，但不覆盖下一个点
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          right: -8px; // 箭头稍微延伸，但不覆盖下一个点
          width: 0;
          height: 0;
          border-left: 8px solid rgba(0, 255, 255, 0.9);
          border-top: 4px solid transparent;
          border-bottom: 4px solid transparent;
          transform: translateY(-50%);
          filter: drop-shadow(0 0 3px rgba(0, 255, 255, 0.8));
          z-index: 10;
        }
      }
    }

    // 当前位置到下一个点的连接线 - 蓝色到黄色渐变
    &.is-current .point-connector {
      .connector-line {
        background: linear-gradient(90deg,
          rgba(24, 144, 255, 0.9) 0%,   // 蓝色（当前位置）
          rgba(24, 144, 255, 0.7) 30%,
          rgba(250, 173, 20, 0.7) 70%,  // 黄色（下一个目标）
          rgba(250, 173, 20, 0.9) 100%
        );
        box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);

        &::after {
          border-left: 8px solid rgba(250, 173, 20, 0.9); // 箭头使用下一个点的颜色
          filter: drop-shadow(0 0 3px rgba(250, 173, 20, 0.8));
        }
      }
    }

    // 已完成的连接线 - 绿色
    &.status-checked .point-connector {
      .connector-line {
        background: linear-gradient(90deg,
          rgba(82, 196, 26, 0.9) 0%,
          rgba(82, 196, 26, 0.7) 50%,
          rgba(82, 196, 26, 0.9) 100%
        );
        box-shadow: 0 0 6px rgba(82, 196, 26, 0.4);

        &::after {
          border-left: 8px solid rgba(82, 196, 26, 0.9);
          filter: drop-shadow(0 0 3px rgba(82, 196, 26, 0.8));
        }
      }
    }
  }
}

// 移除单独的用时信息，已集成到摘要行中

// 当前位置脉冲动画 - 优化层级和发光效果
@keyframes currentPointPulse {
  0%, 100% {
    transform: scale(1.15);
    box-shadow:
      0 0 12px rgba(24, 144, 255, 0.8),
      0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  50% {
    transform: scale(1.25);
    box-shadow:
      0 0 20px rgba(24, 144, 255, 1),
      0 0 0 8px rgba(24, 144, 255, 0.2);
  }
}

// 下一个目标闪烁动画
@keyframes nextPointBlink {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

// 当前位置文字发光动画
@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 8px rgba(24, 144, 255, 1), 0 0 12px rgba(24, 144, 255, 0.6);
  }
}

// 下一个目标文字闪烁动画
@keyframes nextTextBlink {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 3px rgba(250, 173, 20, 0.8);
  }
  50% {
    opacity: 0.8;
    text-shadow: 0 0 6px rgba(250, 173, 20, 1), 0 0 10px rgba(250, 173, 20, 0.6);
  }
}

// 保留原有的脉冲动画，但更新颜色
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(24, 144, 255, 0.6);
  }
}



// 巡更指示器动画
@keyframes patrolIndicator {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
}

// 进度环旋转
@keyframes progressRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 巡更文字动画
@keyframes patrolText {
  0%, 100% {
    opacity: 0.7;
    transform: translateX(-50%) translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-2px);
  }
}

// 等待环旋转
@keyframes waitingRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

// 倒计时动画
@keyframes countdown {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
}

// 下一个目标提醒
@keyframes nextAlert {
  0%, 100% {
    opacity: 0.6;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.1);
  }
}

// 实时进度条
@keyframes progressBar {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}


</style>
