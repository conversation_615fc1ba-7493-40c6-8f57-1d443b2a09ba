<template>
  <div class="plan-video-monitor">
    <!-- 视频监控标签页 -->
    <a-tabs v-model:activeKey="activeTab" type="card" @change="onTabChange">
      <a-tab-pane key="grid" tab="网格视图">
        <template #tab>
          <AppstoreOutlined />
          网格视图 ({{ videoList.length }})
        </template>
        
        <VideoMonitorGrid
          :plan-info="planInfo"
          :video-list="videoList"
          :auto-start-videos="autoStartVideos"
          :default-grid-layout="defaultGridLayout"
        />
      </a-tab-pane>
      
      <a-tab-pane key="list" tab="列表视图">
        <template #tab>
          <UnorderedListOutlined />
          列表视图
        </template>
        
        <div class="video-list-view">
          <div class="list-toolbar">
            <a-space>
              <a-button @click="startAllVideos" :loading="startingAll" size="small">
                <PlayCircleOutlined /> 全部播放
              </a-button>
              <a-button @click="stopAllVideos" size="small">
                <PauseCircleOutlined /> 全部停止
              </a-button>
              <a-button @click="refreshAllVideos" size="small">
                <ReloadOutlined /> 全部刷新
              </a-button>
            </a-space>
            
            <a-space>
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索视频..."
                style="width: 200px"
                @search="onSearch"
              />
              <a-select
                v-model:value="filterType"
                placeholder="筛选类型"
                style="width: 120px"
                @change="onFilterChange"
              >
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="playback">回放流</a-select-option>
                <a-select-option value="preview">预览流</a-select-option>
              </a-select>
            </a-space>
          </div>
          
          <div class="video-list">
            <div
              v-for="(video, index) in filteredVideoList"
              :key="video.id"
              class="video-list-item"
            >
              <div class="video-info">
                <div class="video-header">
                  <h4>{{ video.name }}</h4>
                  <a-tag :color="getStreamTypeColor(video.streamType)">
                    {{ getStreamTypeText(video.streamType) }}
                  </a-tag>
                </div>
                <div class="video-details">
                  <p><strong>RTSP地址:</strong> {{ video.videoUrl }}</p>
                  <p><strong>流ID:</strong> {{ video.streamId }}</p>
                </div>
              </div>
              
              <div class="video-player">
                <VideoMonitorPlayer
                  :ref="el => setVideoPlayerRef(el, index)"
                  :plan-id="planInfo?.id || ''"
                  :video-info="video"
                  :auto-start="false"
                  :show-controls="true"
                />
              </div>
            </div>
          </div>
        </div>
      </a-tab-pane>
      
      <a-tab-pane key="settings" tab="设置">
        <template #tab>
          <SettingOutlined />
          设置
        </template>
        
        <div class="video-settings">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="默认网格布局">
                  <a-select v-model:value="defaultGridLayout">
                    <a-select-option :value="1">1x1 (单画面)</a-select-option>
                    <a-select-option :value="4">2x2 (四画面)</a-select-option>
                    <a-select-option :value="9">3x3 (九画面)</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="自动播放">
                  <a-switch v-model:checked="autoStartVideos" />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="视频质量">
                  <a-select v-model:value="videoQuality">
                    <a-select-option value="high">高清 (1080p)</a-select-option>
                    <a-select-option value="medium">标清 (720p)</a-select-option>
                    <a-select-option value="low">流畅 (480p)</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="音频">
                  <a-switch v-model:checked="audioEnabled" />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-form-item label="连接超时 (秒)">
              <a-input-number v-model:value="connectionTimeout" :min="5" :max="60" />
            </a-form-item>
            
            <a-form-item label="重连次数">
              <a-input-number v-model:value="reconnectAttempts" :min="1" :max="10" />
            </a-form-item>
            
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="saveSettings">保存设置</a-button>
                <a-button @click="resetSettings">重置设置</a-button>
                <a-button @click="testAllConnections">测试连接</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </a-tab-pane>
    </a-tabs>
    
    <!-- 连接测试结果 -->
    <a-modal
      v-model:open="testResultVisible"
      title="连接测试结果"
      :footer="null"
      width="600"
    >
      <div class="test-results">
        <a-list
          :data-source="testResults"
          :loading="testing"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <span>{{ item.name }}</span>
                  <a-tag :color="item.success ? 'green' : 'red'" style="margin-left: 8px">
                    {{ item.success ? '连接成功' : '连接失败' }}
                  </a-tag>
                </template>
                <template #description>
                  <div>
                    <p><strong>RTSP:</strong> {{ item.url }}</p>
                    <p v-if="item.error"><strong>错误:</strong> {{ item.error }}</p>
                    <p v-if="item.responseTime"><strong>响应时间:</strong> {{ item.responseTime }}ms</p>
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  AppstoreOutlined,
  UnorderedListOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import VideoMonitorGrid from './VideoMonitorGrid.vue';
import VideoMonitorPlayer from './VideoMonitorPlayer.vue';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  hlsUrl: string;
  streamType?: string;
}

interface PlanInfo {
  id: string;
  name: string;
}

interface Props {
  planInfo: PlanInfo;
  videoList: VideoInfo[];
}

const props = defineProps<Props>();

// 响应式数据
const activeTab = ref('grid');
const searchKeyword = ref('');
const filterType = ref('');
const startingAll = ref(false);
const testResultVisible = ref(false);
const testing = ref(false);
const testResults = ref<any[]>([]);

// 设置
const defaultGridLayout = ref(4);
const autoStartVideos = ref(false);
const videoQuality = ref('medium');
const audioEnabled = ref(false);
const connectionTimeout = ref(10);
const reconnectAttempts = ref(3);

// 视频播放器引用
const videoPlayerRefs = ref<any[]>([]);

// 计算属性
const filteredVideoList = computed(() => {
  let list = props.videoList;
  
  // 按类型筛选
  if (filterType.value) {
    list = list.filter(video => video.streamType === filterType.value);
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    list = list.filter(video => 
      video.name.toLowerCase().includes(keyword) ||
      video.videoUrl.toLowerCase().includes(keyword)
    );
  }
  
  return list;
});

// 组件挂载
onMounted(() => {
  loadSettings();
});

// 监听视频列表变化
watch(() => props.videoList, () => {
  videoPlayerRefs.value = [];
}, { deep: true });

/**
 * 设置视频播放器引用
 */
function setVideoPlayerRef(el: any, index: number) {
  if (el) {
    videoPlayerRefs.value[index] = el;
  }
}

/**
 * 标签页切换
 */
function onTabChange(key: string) {
  console.log('切换到标签页:', key);
}

/**
 * 搜索
 */
function onSearch(value: string) {
  console.log('搜索:', value);
}

/**
 * 筛选变化
 */
function onFilterChange(value: string) {
  console.log('筛选类型:', value);
}

/**
 * 开始所有视频
 */
async function startAllVideos() {
  startingAll.value = true;
  
  try {
    const promises = videoPlayerRefs.value.map(player => {
      if (player && player.startVideo) {
        return player.startVideo();
      }
      return Promise.resolve();
    });
    
    await Promise.allSettled(promises);
    message.success('已启动所有视频');
  } catch (error) {
    message.error('启动视频失败');
  } finally {
    startingAll.value = false;
  }
}

/**
 * 停止所有视频
 */
function stopAllVideos() {
  videoPlayerRefs.value.forEach(player => {
    if (player && player.stopVideo) {
      player.stopVideo();
    }
  });
  message.info('已停止所有视频');
}

/**
 * 刷新所有视频
 */
function refreshAllVideos() {
  videoPlayerRefs.value.forEach(player => {
    if (player && player.refreshVideo) {
      player.refreshVideo();
    }
  });
  message.info('已刷新所有视频');
}

/**
 * 保存设置
 */
function saveSettings() {
  const settings = {
    defaultGridLayout: defaultGridLayout.value,
    autoStartVideos: autoStartVideos.value,
    videoQuality: videoQuality.value,
    audioEnabled: audioEnabled.value,
    connectionTimeout: connectionTimeout.value,
    reconnectAttempts: reconnectAttempts.value
  };
  
  localStorage.setItem('videoMonitorSettings', JSON.stringify(settings));
  message.success('设置已保存');
}

/**
 * 重置设置
 */
function resetSettings() {
  defaultGridLayout.value = 4;
  autoStartVideos.value = false;
  videoQuality.value = 'medium';
  audioEnabled.value = false;
  connectionTimeout.value = 10;
  reconnectAttempts.value = 3;
  
  message.info('设置已重置');
}

/**
 * 加载设置
 */
function loadSettings() {
  try {
    const saved = localStorage.getItem('videoMonitorSettings');
    if (saved) {
      const settings = JSON.parse(saved);
      defaultGridLayout.value = settings.defaultGridLayout || 4;
      autoStartVideos.value = settings.autoStartVideos || false;
      videoQuality.value = settings.videoQuality || 'medium';
      audioEnabled.value = settings.audioEnabled || false;
      connectionTimeout.value = settings.connectionTimeout || 10;
      reconnectAttempts.value = settings.reconnectAttempts || 3;
    }
  } catch (error) {
    console.error('加载设置失败:', error);
  }
}

/**
 * 测试所有连接
 */
async function testAllConnections() {
  if (props.videoList.length === 0) {
    message.warning('没有可测试的视频');
    return;
  }
  
  testing.value = true;
  testResultVisible.value = true;
  testResults.value = [];
  
  try {
    for (const video of props.videoList) {
      const startTime = Date.now();
      
      try {
        // 这里应该调用后端API测试RTSP连接
        // 暂时模拟测试结果
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
        
        const responseTime = Date.now() - startTime;
        const success = Math.random() > 0.2; // 80%成功率模拟
        
        testResults.value.push({
          name: video.name,
          url: video.videoUrl,
          success,
          responseTime: success ? responseTime : null,
          error: success ? null : '连接超时或认证失败'
        });
      } catch (error) {
        testResults.value.push({
          name: video.name,
          url: video.videoUrl,
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }
    
    const successCount = testResults.value.filter(r => r.success).length;
    message.info(`连接测试完成: ${successCount}/${props.videoList.length} 成功`);
    
  } finally {
    testing.value = false;
  }
}

/**
 * 获取流类型颜色
 */
function getStreamTypeColor(type?: string) {
  switch (type) {
    case 'playback': return 'blue';
    case 'preview': return 'green';
    default: return 'default';
  }
}

/**
 * 获取流类型文本
 */
function getStreamTypeText(type?: string) {
  switch (type) {
    case 'playback': return '回放流';
    case 'preview': return '预览流';
    default: return '未知';
  }
}
</script>

<style scoped>
.plan-video-monitor {
  width: 100%;
  height: 100%;
}

.video-list-view {
  padding: 16px;
}

.list-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.video-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.video-list-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.video-list-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-info {
  flex: 1;
  min-width: 0;
}

.video-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.video-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.video-details p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

.video-player {
  width: 400px;
  flex-shrink: 0;
}

.video-settings {
  padding: 16px;
  max-width: 600px;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .video-list-item {
    flex-direction: column;
  }
  
  .video-player {
    width: 100%;
  }
}
</style>
