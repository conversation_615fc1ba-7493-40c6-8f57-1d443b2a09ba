<template>
  <div class="video-monitor-container">
    <!-- 视频播放区域 -->
    <div class="video-player-wrapper" :class="{ 'fullscreen': isFullscreen }">
      <div class="video-header">
        <div class="video-title">
          <h4>{{ videoInfo?.name || '视频监控' }}</h4>
          <a-tag v-if="videoInfo?.streamType" :color="getStreamTypeColor(videoInfo.streamType)">
            {{ getStreamTypeText(videoInfo.streamType) }}
          </a-tag>
        </div>
        <div class="video-controls">
          <a-button type="primary" @click="startVideo" :loading="starting" :disabled="isPlaying" size="small">
            <PlayCircleOutlined /> 播放
          </a-button>
          <a-button @click="stopVideo" :disabled="!isPlaying" size="small">
            <PauseCircleOutlined /> 停止
          </a-button>
          <a-button @click="refreshVideo" size="small">
            <ReloadOutlined /> 刷新
          </a-button>
          <a-button @click="toggleFullscreen" size="small">
            <ExpandOutlined /> 全屏
          </a-button>
          <a-button @click="captureFrame" :disabled="!isPlaying" size="small">
            <CameraOutlined /> 截图
          </a-button>
        </div>
      </div>
      
      <div class="video-content">
        <!-- 加载状态 -->
        <div v-if="starting" class="video-loading">
          <a-spin size="large">
            <template #indicator>
              <LoadingOutlined style="font-size: 24px" spin />
            </template>
          </a-spin>
          <p>正在连接视频流...</p>
        </div>
        
        <!-- HLS视频播放器 -->
        <div v-else-if="hlsSupported" class="video-wrapper">
          <video
            ref="videoElement"
            class="video-player"
            controls
            autoplay
            muted
            :poster="videoPoster"
            @loadstart="onVideoLoadStart"
            @loadeddata="onVideoLoaded"
            @error="onVideoError"
            @timeupdate="onTimeUpdate"
          >
            您的浏览器不支持视频播放
          </video>
          
          <!-- 视频覆盖层 -->
          <div class="video-overlay" v-if="!isPlaying && !starting">
            <div class="play-button" @click="startVideo">
              <PlayCircleOutlined />
            </div>
          </div>
        </div>
        
        <!-- WebSocket视频流（备用方案） -->
        <div v-else class="video-wrapper">
          <canvas
            ref="canvasElement"
            class="video-canvas"
            :width="canvasWidth"
            :height="canvasHeight"
          ></canvas>
          
          <!-- Canvas覆盖层 -->
          <div class="video-overlay" v-if="!isPlaying && !starting">
            <div class="play-button" @click="startVideo">
              <PlayCircleOutlined />
            </div>
          </div>
        </div>
        
        <!-- 视频信息栏 -->
        <div class="video-info-bar">
          <div class="status-indicators">
            <a-tag :color="isPlaying ? 'green' : 'red'" size="small">
              {{ isPlaying ? '播放中' : '已停止' }}
            </a-tag>
            <a-tag :color="wsConnected ? 'green' : 'red'" size="small">
              {{ wsConnected ? '已连接' : '未连接' }}
            </a-tag>
            <span class="duration">{{ playDuration }}</span>
          </div>
          <div class="video-actions">
            <a-tooltip title="下载截图">
              <a-button type="text" size="small" @click="downloadCapture" :disabled="!lastCapture">
                <DownloadOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="视频设置">
              <a-button type="text" size="small" @click="showSettings">
                <SettingOutlined />
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 错误信息显示 -->
    <a-alert
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      closable
      @close="errorMessage = ''"
      style="margin-top: 10px"
    />
    
    <!-- 视频设置抽屉 -->
    <a-drawer
      v-model:open="settingsVisible"
      title="视频设置"
      placement="right"
      width="300"
    >
      <div class="video-settings">
        <a-form layout="vertical">
          <a-form-item label="视频质量">
            <a-select v-model:value="videoQuality" @change="onQualityChange">
              <a-select-option value="high">高清 (1080p)</a-select-option>
              <a-select-option value="medium">标清 (720p)</a-select-option>
              <a-select-option value="low">流畅 (480p)</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="音频">
            <a-switch v-model:checked="audioEnabled" @change="onAudioChange" />
          </a-form-item>
          
          <a-form-item label="自动播放">
            <a-switch v-model:checked="autoPlay" />
          </a-form-item>
          
          <a-form-item label="循环播放">
            <a-switch v-model:checked="loopPlay" />
          </a-form-item>
        </a-form>
        
        <a-divider />
        
        <div class="video-stats">
          <h4>视频统计</h4>
          <a-descriptions size="small" :column="1">
            <a-descriptions-item label="分辨率">{{ videoResolution }}</a-descriptions-item>
            <a-descriptions-item label="帧率">{{ frameRate }} fps</a-descriptions-item>
            <a-descriptions-item label="比特率">{{ bitRate }} kbps</a-descriptions-item>
            <a-descriptions-item label="延迟">{{ latency }} ms</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  ReloadOutlined, 
  ExpandOutlined,
  CameraOutlined,
  LoadingOutlined,
  DownloadOutlined,
  SettingOutlined
} from '@ant-design/icons-vue';
import Hls from 'hls.js';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  hlsUrl: string;
  streamType?: string;
}

interface Props {
  planId: string;
  videoInfo: VideoInfo;
  autoStart?: boolean;
  showControls?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: false,
  showControls: true
});

// 响应式数据
const videoElement = ref<HTMLVideoElement>();
const canvasElement = ref<HTMLCanvasElement>();
const isPlaying = ref(false);
const starting = ref(false);
const wsConnected = ref(false);
const errorMessage = ref('');
const playStartTime = ref<number>(0);
const playDuration = ref('00:00:00');
const isFullscreen = ref(false);
const settingsVisible = ref(false);
const lastCapture = ref<string>('');

// 视频设置
const videoQuality = ref('medium');
const audioEnabled = ref(false);
const autoPlay = ref(props.autoStart);
const loopPlay = ref(false);

// 视频统计
const videoResolution = ref('--');
const frameRate = ref(0);
const bitRate = ref(0);
const latency = ref(0);

// WebSocket连接
let websocket: WebSocket | null = null;
let hls: Hls | null = null;
let durationTimer: NodeJS.Timeout | null = null;

// 计算属性
const hlsSupported = computed(() => Hls.isSupported() || (videoElement.value?.canPlayType('application/vnd.apple.mpegurl')));
const canvasWidth = computed(() => 640);
const canvasHeight = computed(() => 480);
const videoPoster = computed(() => '/images/video-placeholder.jpg');

// 监听props变化
watch(() => props.videoInfo, (newInfo) => {
  if (newInfo && isPlaying.value) {
    stopVideo();
  }
  if (newInfo && autoPlay.value) {
    nextTick(() => {
      startVideo();
    });
  }
}, { deep: true });

// 组件挂载
onMounted(() => {
  initializePlayer();
  if (autoPlay.value && props.videoInfo) {
    startVideo();
  }
});

// 组件卸载
onUnmounted(() => {
  cleanup();
});

/**
 * 初始化播放器
 */
function initializePlayer() {
  if (!props.videoInfo) {
    errorMessage.value = '视频信息未提供';
    return;
  }
  
  // 初始化WebSocket连接
  initWebSocket();
}

/**
 * 初始化WebSocket连接
 */
function initWebSocket() {
  try {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}${props.videoInfo.websocketUrl}`;
    websocket = new WebSocket(wsUrl);
    
    websocket.onopen = () => {
      wsConnected.value = true;
      console.log('WebSocket连接成功');
    };
    
    websocket.onmessage = (event) => {
      handleWebSocketMessage(event);
    };
    
    websocket.onclose = () => {
      wsConnected.value = false;
      console.log('WebSocket连接关闭');
    };
    
    websocket.onerror = (error) => {
      console.error('WebSocket错误:', error);
      errorMessage.value = 'WebSocket连接失败';
    };
    
  } catch (error) {
    console.error('初始化WebSocket失败:', error);
    errorMessage.value = '初始化WebSocket失败';
  }
}

/**
 * 处理WebSocket消息
 */
function handleWebSocketMessage(event: MessageEvent) {
  try {
    const data = JSON.parse(event.data);
    
    switch (data.type) {
      case 'connected':
        console.log('视频流连接成功');
        break;
      case 'video_started':
        isPlaying.value = true;
        starting.value = false;
        startDurationTimer();
        message.success('视频流启动成功');
        break;
      case 'video_stopped':
        isPlaying.value = false;
        stopDurationTimer();
        message.info('视频流已停止');
        break;
      case 'error':
        errorMessage.value = data.message;
        starting.value = false;
        break;
      case 'stats':
        updateVideoStats(data);
        break;
    }
  } catch (error) {
    console.error('解析WebSocket消息失败:', error);
  }
}

/**
 * 开始播放视频
 */
async function startVideo() {
  if (!props.videoInfo?.videoUrl) {
    errorMessage.value = '视频源地址未设置';
    return;
  }
  
  starting.value = true;
  errorMessage.value = '';
  
  try {
    if (hlsSupported.value) {
      await startHlsVideo();
    } else {
      await startWebSocketVideo();
    }
  } catch (error) {
    console.error('启动视频失败:', error);
    errorMessage.value = '启动视频失败';
    starting.value = false;
  }
}

/**
 * 启动HLS视频播放
 */
async function startHlsVideo() {
  if (!videoElement.value) return;
  
  try {
    if (Hls.isSupported()) {
      hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,
        backBufferLength: 90,
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
      });
      
      hls.loadSource(props.videoInfo.hlsUrl);
      hls.attachMedia(videoElement.value);
      
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        videoElement.value?.play();
        isPlaying.value = true;
        starting.value = false;
        startDurationTimer();
      });
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS错误:', data);
        errorMessage.value = `HLS播放错误: ${data.details}`;
        starting.value = false;
      });
      
    } else if (videoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari原生支持
      videoElement.value.src = props.videoInfo.hlsUrl;
      await videoElement.value.play();
      isPlaying.value = true;
      starting.value = false;
      startDurationTimer();
    }
    
    // 通知后端启动视频流转换
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'start_video',
        rtspUrl: props.videoInfo.videoUrl,
        quality: videoQuality.value,
        audio: audioEnabled.value
      }));
    }
    
  } catch (error) {
    console.error('HLS播放失败:', error);
    throw error;
  }
}

/**
 * 启动WebSocket视频播放
 */
async function startWebSocketVideo() {
  if (!websocket || websocket.readyState !== WebSocket.OPEN) {
    throw new Error('WebSocket未连接');
  }
  
  // 发送启动视频流请求
  websocket.send(JSON.stringify({
    type: 'start_video',
    rtspUrl: props.videoInfo.videoUrl,
    quality: videoQuality.value,
    audio: audioEnabled.value
  }));
}

/**
 * 停止视频播放
 */
function stopVideo() {
  try {
    // 停止HLS播放
    if (hls) {
      hls.destroy();
      hls = null;
    }
    
    // 停止视频元素
    if (videoElement.value) {
      videoElement.value.pause();
      videoElement.value.src = '';
    }
    
    // 通知后端停止视频流
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'stop_video'
      }));
    }
    
    isPlaying.value = false;
    stopDurationTimer();
    
  } catch (error) {
    console.error('停止视频失败:', error);
  }
}

/**
 * 刷新视频
 */
function refreshVideo() {
  stopVideo();
  setTimeout(() => {
    startVideo();
  }, 1000);
}

/**
 * 切换全屏
 */
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value;
  
  if (isFullscreen.value) {
    if (videoElement.value?.requestFullscreen) {
      videoElement.value.requestFullscreen();
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
}

/**
 * 截图
 */
function captureFrame() {
  if (!videoElement.value || !isPlaying.value) return;
  
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = videoElement.value.videoWidth;
    canvas.height = videoElement.value.videoHeight;
    
    ctx?.drawImage(videoElement.value, 0, 0);
    
    lastCapture.value = canvas.toDataURL('image/png');
    message.success('截图成功');
    
  } catch (error) {
    console.error('截图失败:', error);
    message.error('截图失败');
  }
}

/**
 * 下载截图
 */
function downloadCapture() {
  if (!lastCapture.value) return;
  
  const link = document.createElement('a');
  link.download = `screenshot_${Date.now()}.png`;
  link.href = lastCapture.value;
  link.click();
}

/**
 * 显示设置
 */
function showSettings() {
  settingsVisible.value = true;
}

/**
 * 质量变化
 */
function onQualityChange() {
  if (isPlaying.value) {
    refreshVideo();
  }
}

/**
 * 音频变化
 */
function onAudioChange() {
  if (videoElement.value) {
    videoElement.value.muted = !audioEnabled.value;
  }
}

/**
 * 获取流类型颜色
 */
function getStreamTypeColor(type: string) {
  switch (type) {
    case 'playback': return 'blue';
    case 'preview': return 'green';
    default: return 'default';
  }
}

/**
 * 获取流类型文本
 */
function getStreamTypeText(type: string) {
  switch (type) {
    case 'playback': return '回放流';
    case 'preview': return '预览流';
    default: return '未知';
  }
}

/**
 * 视频事件处理
 */
function onVideoLoadStart() {
  console.log('视频开始加载');
}

function onVideoLoaded() {
  console.log('视频加载完成');
  if (videoElement.value) {
    videoResolution.value = `${videoElement.value.videoWidth}x${videoElement.value.videoHeight}`;
  }
}

function onVideoError(event: Event) {
  console.error('视频播放错误:', event);
  errorMessage.value = '视频播放错误';
}

function onTimeUpdate() {
  // 更新播放时间等信息
}

/**
 * 更新视频统计
 */
function updateVideoStats(data: any) {
  frameRate.value = data.frameRate || 0;
  bitRate.value = data.bitRate || 0;
  latency.value = data.latency || 0;
}

/**
 * 开始计时器
 */
function startDurationTimer() {
  playStartTime.value = Date.now();
  durationTimer = setInterval(() => {
    const elapsed = Date.now() - playStartTime.value;
    playDuration.value = formatDuration(elapsed);
  }, 1000);
}

/**
 * 停止计时器
 */
function stopDurationTimer() {
  if (durationTimer) {
    clearInterval(durationTimer);
    durationTimer = null;
  }
  playDuration.value = '00:00:00';
}

/**
 * 格式化时长
 */
function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 清理资源
 */
function cleanup() {
  stopVideo();
  
  if (websocket) {
    websocket.close();
    websocket = null;
  }
  
  stopDurationTimer();
}

// 暴露方法给父组件
defineExpose({
  startVideo,
  stopVideo,
  refreshVideo,
  captureFrame,
  isPlaying: () => isPlaying.value
});
</script>

<style scoped>
.video-monitor-container {
  width: 100%;
  height: 100%;
}

.video-player-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
  transition: all 0.3s ease;
}

.video-player-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.video-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-title h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.video-controls {
  display: flex;
  gap: 6px;
}

.video-content {
  position: relative;
  background: #000;
}

.video-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: white;
  gap: 16px;
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 300px;
  background: #000;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-canvas {
  display: block;
  margin: 0 auto;
  background: #000;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.video-overlay:hover {
  background: rgba(0, 0, 0, 0.5);
}

.play-button {
  font-size: 48px;
  color: white;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.play-button:hover {
  opacity: 1;
  transform: scale(1.1);
}

.video-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-top: 1px solid #d9d9d9;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 8px;
}

.duration {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
}

.video-actions {
  display: flex;
  gap: 4px;
}

.video-settings {
  padding: 16px 0;
}

.video-stats h4 {
  margin-bottom: 12px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-header {
    flex-direction: column;
    gap: 8px;
    padding: 12px;
  }
  
  .video-controls {
    width: 100%;
    justify-content: center;
  }
  
  .video-wrapper {
    height: 200px;
  }
  
  .video-info-bar {
    flex-direction: column;
    gap: 8px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.video-loading .anticon {
  animation: pulse 2s infinite;
}
</style>
