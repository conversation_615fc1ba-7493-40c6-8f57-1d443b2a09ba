<template>
  <div class="video-monitor-player-modal">
    <!-- 视频播放区域 -->
    <div class="video-player-wrapper">
      <div class="video-header">
        <div class="video-title">
          <h5>{{ videoInfo?.name || '视频监控' }}</h5>
          <a-tag v-if="videoInfo?.streamType" :color="getStreamTypeColor(videoInfo.streamType)" size="small">
            {{ getStreamTypeText(videoInfo.streamType) }}
          </a-tag>
        </div>
        <div class="video-controls" v-if="showControls">
          <a-button
            type="primary"
            @click="startVideo"
            :loading="starting"
            :disabled="isPlaying"
            size="small"
            title="开始播放"
          >
            <PlayCircleOutlined />
            {{ starting ? '启动中...' : '播放' }}
          </a-button>
          <a-button
            @click="stopVideo"
            :disabled="!isPlaying && !starting"
            size="small"
            title="停止播放"
          >
            <PauseCircleOutlined />
            停止
          </a-button>
          <a-button
            @click="refreshVideo"
            :disabled="starting"
            size="small"
            title="刷新视频"
          >
            <ReloadOutlined />
            刷新
          </a-button>
          <a-button
            @click="captureFrame"
            :disabled="!isPlaying"
            size="small"
            title="截图"
          >
            <CameraOutlined />
            截图
          </a-button>
        </div>
      </div>
      
      <div class="video-content">
        <!-- 加载状态 -->
        <div v-if="starting" class="video-loading">
          <a-spin size="large">
            <template #indicator>
              <LoadingOutlined style="font-size: 24px" spin />
            </template>
          </a-spin>
          <p>正在连接视频流...</p>
        </div>
        
        <!-- HLS视频播放器 -->
        <div v-else-if="hlsSupported" class="video-wrapper">
          <video
            ref="videoElement"
            class="video-player"
            controls
            autoplay
            muted
            :poster="videoPoster"
            @loadstart="onVideoLoadStart"
            @loadeddata="onVideoLoaded"
            @error="onVideoError"
            @play="onVideoPlay"
            @pause="onVideoPause"
            @ended="onVideoEnded"
            @waiting="onVideoWaiting"
            @canplay="onVideoCanPlay"
            @stalled="onVideoStalled"
          >
            您的浏览器不支持视频播放
          </video>
          
          <!-- 视频覆盖层 -->
          <div class="video-overlay" v-if="!isPlaying && !starting">
            <div class="play-button" @click="startVideo">
              <PlayCircleOutlined />
              <span class="play-text">点击播放</span>
            </div>
          </div>

          <!-- 启动中覆盖层 -->
          <div class="video-overlay starting-overlay" v-if="starting">
            <div class="starting-content">
              <LoadingOutlined class="loading-icon" />
              <span class="starting-text">正在启动视频流...</span>
            </div>
          </div>
        </div>
        
        <!-- WebSocket视频流（备用方案） -->
        <div v-else class="video-wrapper">
          <canvas
            ref="canvasElement"
            class="video-canvas"
            :width="canvasWidth"
            :height="canvasHeight"
          ></canvas>
          
          <!-- Canvas覆盖层 -->
          <div class="video-overlay" v-if="!isPlaying && !starting">
            <div class="play-button" @click="startVideo">
              <PlayCircleOutlined />
            </div>
          </div>
        </div>
        
        <!-- 视频信息栏 -->
        <div class="video-info-bar" v-if="showControls">
          <div class="status-indicators">
            <a-tag :color="isPlaying ? 'green' : 'red'" size="small">
              {{ isPlaying ? '播放中' : '已停止' }}
            </a-tag>
            <a-tag :color="wsConnected ? 'green' : 'red'" size="small">
              {{ wsConnected ? '已连接' : '未连接' }}
            </a-tag>
            <span class="duration">{{ playDuration }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 错误信息显示 -->
    <a-alert
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      closable
      @close="errorMessage = ''"
      style="margin-top: 8px"
      size="small"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  CameraOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue';
import Hls from 'hls.js';
import { useMyWebSocket, onWebSocket, offWebSocket } from '/@/hooks/web/useWebSocket';
import { defHttp } from '/@/utils/http/axios';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  hlsUrl: string;
  streamType?: string;
}

interface Props {
  planId: string;
  videoInfo: VideoInfo;
  autoStart?: boolean;
  showControls?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: false,
  showControls: true
});

// 响应式数据
const videoElement = ref<HTMLVideoElement>();
const canvasElement = ref<HTMLCanvasElement>();
const isPlaying = ref(false);
const starting = ref(false);
const wsConnected = ref(false);
const errorMessage = ref('');
const playStartTime = ref<number>(0);
const playDuration = ref('00:00:00');

// 使用系统WebSocket
const wsResult = useMyWebSocket();

let hls: Hls | null = null;
let durationTimer: NodeJS.Timeout | null = null;
let reconnectTimer: NodeJS.Timeout | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
let healthCheckTimer: NodeJS.Timeout | null = null;

// 计算属性
const hlsSupported = computed(() => Hls.isSupported() || (videoElement.value?.canPlayType('application/vnd.apple.mpegurl')));
const canvasWidth = computed(() => 320);
const canvasHeight = computed(() => 240);
const videoPoster = computed(() => '/images/video-placeholder.jpg');

// 监听props变化
watch(() => props.videoInfo, (newInfo) => {
  if (newInfo && isPlaying.value) {
    stopVideo();
  }
  if (newInfo && props.autoStart) {
    nextTick(() => {
      startVideo();
    });
  }
}, { deep: true });

// 组件挂载
onMounted(() => {
  initializePlayer();

  // 监听系统WebSocket消息
  onWebSocket(handleVideoMessage);

  if (props.autoStart && props.videoInfo) {
    startVideo();
  }
});

// 组件卸载
onUnmounted(() => {
  // 移除WebSocket监听器
  offWebSocket(handleVideoMessage);
  cleanup();
});

/**
 * 初始化播放器
 */
function initializePlayer() {
  if (!props.videoInfo) {
    errorMessage.value = '视频信息未提供';
    return;
  }

  // 系统WebSocket已经在useWebSocket中初始化，这里不需要额外初始化
  wsConnected.value = true; // 假设系统WebSocket已连接
}

/**
 * 处理系统WebSocket消息
 */
function handleVideoMessage(data: any) {
  try {
    if (data.msgType === 'video' && data.data) {
      const videoData = data.data;

      // 检查是否是当前流的消息
      if (videoData.streamId !== props.videoInfo.streamId) {
        return;
      }

      switch (videoData.type) {
        case 'video_started':
          isPlaying.value = true;
          starting.value = false;
          startDurationTimer();
          message.success('视频流启动成功');
          break;
        case 'video_stopped':
          isPlaying.value = false;
          stopDurationTimer();
          message.info('视频流已停止');
          break;
        case 'video_error':
          errorMessage.value = videoData.message;
          starting.value = false;
          message.error('视频流错误: ' + videoData.message);
          break;
        case 'video_frame':
          // 处理视频帧数据（如果需要）
          break;
      }
    }
  } catch (error) {
    console.error('处理视频WebSocket消息失败:', error);
  }
}

/**
 * 开始播放视频
 */
async function startVideo() {
  if (!props.videoInfo?.videoUrl && !props.videoInfo?.streamId) {
    errorMessage.value = '视频源地址或流ID未设置';
    message.error('视频源地址或流ID未设置');
    return;
  }

  if (isPlaying.value) {
    console.log('视频已在播放中');
    return;
  }

  starting.value = true;
  errorMessage.value = '';

  console.log('开始播放视频:', {
    videoUrl: props.videoInfo.videoUrl,
    streamId: props.videoInfo.streamId,
    hlsSupported: hlsSupported.value
  });

  try {
    // 首先停止任何现有的播放
    if (hls) {
      hls.destroy();
      hls = null;
    }

    if (hlsSupported.value) {
      await startHlsVideo();
    } else {
      await startWebSocketVideo();
    }
  } catch (error: any) {
    console.error('启动视频失败:', error);
    errorMessage.value = '启动视频失败: ' + (error?.message || error);
    starting.value = false;
    message.error('启动视频失败: ' + (error?.message || error));
  }
}

/**
 * 启动HLS视频播放
 */
async function startHlsVideo() {
  if (!videoElement.value) {
    throw new Error('视频元素未找到');
  }

  try {
    console.log('开始启动HLS视频播放:', {
      hlsUrl: props.videoInfo.hlsUrl,
      videoUrl: props.videoInfo.videoUrl,
      streamId: props.videoInfo.streamId
    });

    // 首先通过HTTP API启动视频流转换
    await startVideoStreamAPI();

    // 等待更长时间让后端开始转换，并检查流状态
    await waitForStreamReady();

    if (Hls.isSupported()) {
      console.log('使用HLS.js播放视频');
      hls = new Hls({
        debug: false, // 关闭调试模式减少日志
        enableWorker: true,
        lowLatencyMode: true,
        // 优化缓冲配置
        maxBufferLength: 10, // 减少最大缓冲长度
        maxMaxBufferLength: 30, // 减少最大缓冲长度
        maxBufferSize: 30 * 1000 * 1000, // 减少缓冲大小
        maxBufferHole: 0.3,
        highBufferWatchdogPeriod: 1,
        nudgeOffset: 0.05,
        nudgeMaxRetry: 5,
        maxFragLookUpTolerance: 0.2,
        liveSyncDurationCount: 2, // 减少同步片段数量
        liveMaxLatencyDurationCount: 5, // 限制最大延迟
        liveDurationInfinity: false,
        enableSoftwareAES: true,
        // 优化加载超时配置
        manifestLoadingTimeOut: 15000, // 增加清单加载超时
        manifestLoadingMaxRetry: 3, // 增加重试次数
        manifestLoadingRetryDelay: 500, // 减少重试延迟
        levelLoadingTimeOut: 15000, // 增加级别加载超时
        levelLoadingMaxRetry: 6, // 增加重试次数
        levelLoadingRetryDelay: 500, // 减少重试延迟
        fragLoadingTimeOut: 30000, // 增加片段加载超时
        fragLoadingMaxRetry: 10, // 增加片段重试次数
        fragLoadingRetryDelay: 500, // 减少重试延迟
        startFragPrefetch: true, // 启用片段预取
        testBandwidth: false, // 关闭带宽测试
        // 添加自动恢复配置
        autoStartLoad: true,
        startPosition: -1, // 从最新位置开始
        capLevelToPlayerSize: false
      });

      // 构建HLS URL，添加时间戳避免缓存
      const timestamp = Date.now();
      const hlsUrl = props.videoInfo.hlsUrl || `/api/video/hls/${props.videoInfo.streamId}/index.m3u8?t=${timestamp}`;
      console.log('加载HLS源:', hlsUrl);

      hls.loadSource(hlsUrl);
      hls.attachMedia(videoElement.value);

      // 添加更多事件监听
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS清单解析完成，开始播放');
        videoElement.value?.play().then(() => {
          isPlaying.value = true;
          starting.value = false;
          startDurationTimer();
          clearReconnectTimer(); // 播放成功，清除重连定时器
          startHealthCheck(); // 开始健康检查
          message.success('视频播放成功');
        }).catch(error => {
          console.error('视频播放失败:', error);
          errorMessage.value = '视频播放失败: ' + error.message;
          starting.value = false;
        });
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS错误:', event, data);
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('网络错误，尝试恢复...');
              if (hls) {
                hls.startLoad();
              }
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('媒体错误，尝试恢复...');
              if (hls) {
                hls.recoverMediaError();
              }
              break;
            default:
              console.log('无法恢复的错误，尝试自动重连...');
              if (hls) {
                hls.destroy();
                hls = null;
              }
              isPlaying.value = false;
              starting.value = false;

              // 尝试自动重连
              attemptAutoReconnect();
              break;
          }
        } else {
          console.warn('HLS非致命错误:', data);
        }
      });

      // 添加更多事件监听以便调试和恢复
      hls.on(Hls.Events.FRAG_LOADED, () => {
        console.log('HLS片段加载完成');
      });

      hls.on(Hls.Events.LEVEL_SWITCHED, (_event, data) => {
        console.log('HLS级别切换:', data.level);
      });

      hls.on(Hls.Events.BUFFER_APPENDED, () => {
        console.log('HLS缓冲区已添加数据');
      });

      hls.on(Hls.Events.BUFFER_EOS, () => {
        console.log('HLS缓冲区结束');
      });

      hls.on(Hls.Events.FRAG_PARSING_USERDATA, () => {
        // 片段正在解析，表示流正常
        if (!isPlaying.value && videoElement.value && !videoElement.value.paused) {
          isPlaying.value = true;
          starting.value = false;
        }
      });

    } else if (videoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
      console.log('使用Safari原生HLS支持');
      // Safari原生支持
      const hlsUrl = props.videoInfo.hlsUrl || `/api/video/hls/${props.videoInfo.streamId}/index.m3u8`;
      videoElement.value.src = hlsUrl;
      await videoElement.value.play();
      isPlaying.value = true;
      starting.value = false;
      startDurationTimer();
      message.success('视频播放成功');
    } else {
      throw new Error('浏览器不支持HLS播放');
    }

  } catch (error: any) {
    console.error('HLS播放失败:', error);
    errorMessage.value = 'HLS播放失败: ' + (error?.message || error);
    starting.value = false;
    throw error;
  }
}

/**
 * 启动WebSocket视频播放
 */
async function startWebSocketVideo() {
  // 通过HTTP API启动视频流转换
  await startVideoStreamAPI();
}

/**
 * 停止视频播放
 */
async function stopVideo() {
  try {
    console.log('停止视频播放:', props.videoInfo.streamId);

    // 停止HLS播放
    if (hls) {
      console.log('销毁HLS实例');
      hls.destroy();
      hls = null;
    }

    // 停止视频元素
    if (videoElement.value) {
      console.log('停止视频元素');
      videoElement.value.pause();
      videoElement.value.src = '';
      videoElement.value.load(); // 重置视频元素
    }

    // 通过HTTP API停止视频流
    await stopVideoStreamAPI();

    isPlaying.value = false;
    starting.value = false;
    stopDurationTimer();
    stopHealthCheck(); // 停止健康检查
    clearReconnectTimer(); // 清除重连定时器
    errorMessage.value = '';

    console.log('视频播放已停止');

  } catch (error: any) {
    console.error('停止视频失败:', error);
    errorMessage.value = '停止视频失败: ' + (error?.message || error);
  }
}

/**
 * 刷新视频
 */
async function refreshVideo() {
  console.log('刷新视频...');

  try {
    // 停止当前播放
    await stopVideo();

    // 等待一段时间确保资源完全释放
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 重新启动播放
    await startVideo();

    message.info('视频已刷新');
  } catch (error: any) {
    console.error('刷新视频失败:', error);
    errorMessage.value = '刷新视频失败: ' + (error?.message || error);
    message.error('刷新视频失败');
  }
}

/**
 * 截图
 */
function captureFrame() {
  if (!videoElement.value || !isPlaying.value) return;
  
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = videoElement.value.videoWidth;
    canvas.height = videoElement.value.videoHeight;
    
    ctx?.drawImage(videoElement.value, 0, 0);
    
    const dataUrl = canvas.toDataURL('image/png');
    
    // 下载截图
    const link = document.createElement('a');
    link.download = `screenshot_${Date.now()}.png`;
    link.href = dataUrl;
    link.click();
    
    message.success('截图成功');
    
  } catch (error) {
    console.error('截图失败:', error);
    message.error('截图失败');
  }
}

/**
 * 获取流类型颜色
 */
function getStreamTypeColor(type: string) {
  switch (type) {
    case 'playback': return 'blue';
    case 'preview': return 'green';
    default: return 'default';
  }
}

/**
 * 获取流类型文本
 */
function getStreamTypeText(type: string) {
  switch (type) {
    case 'playback': return '回放流';
    case 'preview': return '预览流';
    default: return '未知';
  }
}

/**
 * 视频事件处理
 */
function onVideoLoadStart() {
  console.log('视频开始加载');
}

function onVideoLoaded() {
  console.log('视频加载完成');
}

function onVideoError(event: Event) {
  console.error('视频播放错误:', event);
  errorMessage.value = '视频播放错误';
  isPlaying.value = false;
  starting.value = false;
  attemptAutoReconnect();
}

function onVideoPlay() {
  console.log('视频开始播放');
  isPlaying.value = true;
  starting.value = false;
  clearReconnectTimer();
}

function onVideoPause() {
  console.log('视频暂停');
  // 注意：不要在这里设置 isPlaying.value = false，因为可能是缓冲导致的暂停
}

function onVideoEnded() {
  console.log('视频播放结束');
  isPlaying.value = false;
  stopDurationTimer();
  stopHealthCheck();
}

function onVideoWaiting() {
  console.log('视频等待数据...');
  // 视频在等待数据，可能是网络问题
}

function onVideoCanPlay() {
  console.log('视频可以播放');
  if (!isPlaying.value && videoElement.value && !videoElement.value.paused) {
    isPlaying.value = true;
    starting.value = false;
  }
}

function onVideoStalled() {
  console.log('视频播放停滞');
  // 视频播放停滞，可能需要重新加载
  if (hls) {
    hls.startLoad();
  }
}

/**
 * 开始计时器
 */
function startDurationTimer() {
  playStartTime.value = Date.now();
  durationTimer = setInterval(() => {
    const elapsed = Date.now() - playStartTime.value;
    playDuration.value = formatDuration(elapsed);
  }, 1000);
}

/**
 * 停止计时器
 */
function stopDurationTimer() {
  if (durationTimer) {
    clearInterval(durationTimer);
    durationTimer = null;
  }
  playDuration.value = '00:00:00';
}

/**
 * 格式化时长
 */
function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 尝试自动重连
 */
function attemptAutoReconnect() {
  if (reconnectAttempts >= maxReconnectAttempts) {
    console.log('已达到最大重连次数，停止重连');
    errorMessage.value = '视频连接失败，已达到最大重连次数';
    return;
  }

  reconnectAttempts++;
  const delay = Math.min(1000 * reconnectAttempts, 5000); // 递增延迟，最大5秒

  console.log(`第 ${reconnectAttempts} 次自动重连，${delay}ms 后开始...`);

  reconnectTimer = setTimeout(async () => {
    try {
      console.log('开始自动重连...');
      await startVideo();
      reconnectAttempts = 0; // 重连成功，重置计数器
      message.success('视频重连成功');
    } catch (error: any) {
      console.error('自动重连失败:', error);
      // 继续尝试重连
      attemptAutoReconnect();
    }
  }, delay);
}

/**
 * 清除重连定时器
 */
function clearReconnectTimer() {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
  reconnectAttempts = 0;
}

/**
 * 开始健康检查
 */
function startHealthCheck() {
  stopHealthCheck(); // 先停止之前的检查

  healthCheckTimer = setInterval(() => {
    if (!isPlaying.value || !videoElement.value) {
      return;
    }

    // 检查视频是否真正在播放
    const video = videoElement.value;
    const currentTime = video.currentTime;

    // 等待一段时间后再次检查
    setTimeout(() => {
      if (isPlaying.value && video.currentTime === currentTime && !video.paused) {
        console.warn('视频可能已停止播放，尝试恢复...');
        // 视频时间没有变化，可能已停止
        if (hls) {
          hls.startLoad();
        } else {
          // 如果不是HLS，尝试重新播放
          video.play().catch(error => {
            console.error('重新播放失败:', error);
            attemptAutoReconnect();
          });
        }
      }
    }, 3000); // 3秒后检查
  }, 10000); // 每10秒检查一次
}

/**
 * 停止健康检查
 */
function stopHealthCheck() {
  if (healthCheckTimer) {
    clearInterval(healthCheckTimer);
    healthCheckTimer = null;
  }
}

/**
 * 等待流准备就绪
 */
async function waitForStreamReady() {
  const maxRetries = 10; // 最大重试次数
  const retryDelay = 1000; // 重试间隔（毫秒）

  for (let i = 0; i < maxRetries; i++) {
    try {
      console.log(`检查流状态，第 ${i + 1} 次尝试...`);

      // 构建检查URL
      const checkUrl = props.videoInfo.hlsUrl || `/api/video/hls/${props.videoInfo.streamId}/index.m3u8`;

      // 发送HEAD请求检查流是否可用
      const response = await fetch(checkUrl, {
        method: 'HEAD',
        cache: 'no-cache'
      });

      if (response.ok) {
        console.log('流已准备就绪');
        return;
      }

      console.log(`流尚未准备就绪，状态码: ${response.status}`);
    } catch (error) {
      console.log(`检查流状态失败: ${error}`);
    }

    // 等待后重试
    if (i < maxRetries - 1) {
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  console.warn('流准备检查超时，继续尝试播放...');
}

/**
 * 通过HTTP API启动视频流
 */
async function startVideoStreamAPI() {
  try {
    console.log('启动视频流API调用:', {
      streamId: props.videoInfo.streamId,
      rtspUrl: props.videoInfo.videoUrl
    });

    const response = await defHttp.post({
      url: '/video/start',
      data: {
        streamId: props.videoInfo.streamId,
        rtspUrl: props.videoInfo.videoUrl,
        userId: null // 后端会自动获取当前用户ID
      }
    });

    console.log('启动视频流API响应:', response);
    return response;
  } catch (error) {
    console.error('启动视频流API调用失败:', error);
    throw error;
  }
}

/**
 * 通过HTTP API停止视频流
 */
async function stopVideoStreamAPI() {
  try {
    console.log('停止视频流API调用:', {
      streamId: props.videoInfo.streamId
    });

    const response = await defHttp.post({
      url: '/video/stop',
      data: {
        streamId: props.videoInfo.streamId,
        userId: null // 后端会自动获取当前用户ID
      }
    });

    console.log('停止视频流API响应:', response);
    return response;
  } catch (error) {
    console.error('停止视频流API调用失败:', error);
  }
}

/**
 * 清理资源
 */
function cleanup() {
  clearReconnectTimer(); // 清除重连定时器
  stopHealthCheck(); // 停止健康检查
  stopVideo();
  stopDurationTimer();
}

// 暴露方法给父组件
defineExpose({
  startVideo,
  stopVideo,
  refreshVideo,
  captureFrame,
  isPlaying: () => isPlaying.value
});
</script>

<style scoped>
.video-monitor-player-modal {
  width: 100%;
  height: 100%;
}

.video-player-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  flex-shrink: 0;
}

.video-title {
  display: flex;
  align-items: center;
  gap: 6px;
}

.video-title h5 {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.video-controls {
  display: flex;
  gap: 4px;
}

.video-content {
  position: relative;
  background: #000;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: white;
  gap: 12px;
}

.video-wrapper {
  position: relative;
  width: 100%;
  flex: 1;
  background: #000;
  min-height: 120px;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-canvas {
  display: block;
  margin: 0 auto;
  background: #000;
  width: 100%;
  height: 100%;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.video-overlay:hover {
  background: rgba(0, 0, 0, 0.5);
}

.play-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-size: 48px;
  color: white;
  opacity: 0.9;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 20px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.play-button:hover {
  opacity: 1;
  transform: scale(1.1);
  background: rgba(0, 0, 0, 0.5);
}

.play-text {
  font-size: 14px;
  font-weight: 500;
  margin-top: 4px;
}

.starting-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.starting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: white;
}

.loading-icon {
  font-size: 32px;
  animation: spin 1s linear infinite;
}

.starting-text {
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.video-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #f5f5f5;
  border-top: 1px solid #d9d9d9;
  flex-shrink: 0;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 6px;
}

.duration {
  font-family: 'Courier New', monospace;
  font-size: 10px;
  color: #666;
}
</style>
